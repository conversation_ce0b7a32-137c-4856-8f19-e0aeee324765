
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named org - imported by pickle (optional)
missing module named pwd - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), posixpath (delayed, conditional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), webbrowser (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), pip._vendor.distlib._backport.tarfile (optional), pip._vendor.distlib._backport.shutil (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), distutils.archive_util (optional), pip._vendor.distlib._backport.tarfile (optional), pip._vendor.distlib._backport.shutil (optional)
missing module named posix - imported by shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named urllib.splittype - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.ContentTooShortError - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.pathname2url - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.url2pathname - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.urlretrieve - imported by urllib (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.getproxies_environment - imported by urllib (conditional), pip._vendor.requests.compat (conditional)
missing module named urllib.proxy_bypass_environment - imported by urllib (conditional), pip._vendor.requests.compat (conditional)
missing module named urllib.proxy_bypass - imported by urllib (conditional), pip._vendor.requests.compat (conditional)
missing module named urllib.getproxies - imported by urllib (conditional), pip._vendor.requests.compat (conditional)
missing module named urllib.urlencode - imported by urllib (conditional), pip._vendor.requests.compat (conditional)
missing module named urllib.unquote_plus - imported by urllib (conditional), pip._vendor.requests.compat (conditional)
missing module named urllib.quote_plus - imported by urllib (conditional), pip._vendor.requests.compat (conditional)
missing module named urllib.unquote - imported by urllib (conditional), pip._vendor.requests.compat (conditional), pip._vendor.distlib.compat (conditional)
missing module named urllib.quote - imported by urllib (conditional), pip._vendor.requests.compat (conditional), pip._vendor.distlib.compat (conditional)
missing module named pep517 - imported by importlib.metadata (delayed)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level), pip._vendor.distlib.resources (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level), pip._vendor.distlib.resources (optional)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named fcntl - imported by subprocess (optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named pyimod02_importers - imported by E:\Desktop\企业微信自动回复系统\企业微信自动回复系统\app\venv_build\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named collections.Iterable - imported by collections (optional), pip._vendor.pyparsing (optional)
missing module named collections.Callable - imported by collections (conditional), pip._vendor.requests.compat (conditional), pip._vendor.distlib._backport.shutil (optional)
missing module named collections.MutableMapping - imported by collections (optional), pip._vendor.urllib3._collections (optional), pip._vendor.requests.compat (conditional), pip._vendor.pyparsing (optional), pip._vendor.html5lib.treebuilders.dom (optional), pip._vendor.html5lib.treebuilders.etree_lxml (optional), pip._vendor.distlib.compat (optional)
missing module named collections.Mapping - imported by collections (optional), pip._vendor.urllib3._collections (optional), pip._vendor.requests.compat (conditional), pip._vendor.pyparsing (optional), pip._vendor.html5lib._utils (optional), pip._vendor.html5lib._trie._base (optional)
missing module named PIL - imported by common.utils (top-level), channel.wework.wework_channel (top-level)
missing module named pilk - imported by voice.audio_convert (optional), channel.wework.wework_message (top-level)
missing module named pydub - imported by voice.audio_convert (optional), voice.xunfei.xunfei_voice (top-level)
missing module named pysilk - imported by voice.audio_convert (optional)
missing module named dulwich - imported by common.package_manager (delayed, optional), plugins.plugin_manager (delayed)
missing module named 'com.sun' - imported by pip._vendor.appdirs (delayed, conditional, optional)
missing module named com - imported by pip._vendor.appdirs (delayed)
missing module named win32api - imported by pip._vendor.appdirs (delayed, conditional, optional)
missing module named win32com - imported by pip._vendor.appdirs (delayed)
missing module named _winreg - imported by platform (delayed, optional), pip._vendor.appdirs (delayed, conditional), pip._vendor.requests.utils (delayed, conditional, optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed), pip._vendor.distlib.scripts (delayed, conditional)
missing module named 'pip._vendor.six.moves' - imported by pip._vendor.pkg_resources (top-level), pip._vendor.html5lib._inputstream (top-level), pip._vendor.html5lib.filters.sanitizer (top-level)
missing module named lxml - imported by pip._vendor.html5lib.treebuilders.etree_lxml (top-level), pip._vendor.html5lib.treewalkers.etree_lxml (top-level)
missing module named 'genshi.core' - imported by pip._vendor.html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by pip._vendor.html5lib.treewalkers.genshi (top-level)
missing module named pip._vendor.msgpack._cmsgpack - imported by pip._vendor.msgpack (conditional, optional)
missing module named '__pypy__.builders' - imported by pip._vendor.msgpack.fallback (conditional, optional)
missing module named __pypy__ - imported by pip._vendor.msgpack.fallback (conditional)
missing module named StringIO - imported by pip._vendor.urllib3.packages.six (conditional), pip._vendor.six (conditional), pip._vendor.requests.compat (conditional), pip._vendor.distlib.compat (conditional)
missing module named wheel - imported by pip._internal.utils.misc (delayed, optional)
missing module named _manylinux - imported by pip._vendor.packaging._manylinux (delayed, optional)
missing module named readline - imported by site (delayed, optional), rlcompleter (optional), cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional)
missing module named __builtin__ - imported by pip._vendor.pyparsing (conditional), pip._vendor.distlib.compat (conditional), pip._vendor.distlib._backport.tarfile (conditional)
missing module named ordereddict - imported by pip._vendor.pyparsing (optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named backports - imported by pip._vendor.urllib3.packages.ssl_match_hostname (optional)
missing module named 'pip._vendor.urllib3.packages.six.moves' - imported by pip._vendor.urllib3.exceptions (top-level), pip._vendor.urllib3.connection (top-level), pip._vendor.urllib3.util.response (top-level), pip._vendor.urllib3.connectionpool (top-level), pip._vendor.urllib3.request (top-level), pip._vendor.urllib3.util.queue (top-level), pip._vendor.urllib3.poolmanager (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional), pip._vendor.urllib3.util.request (optional), pip._vendor.urllib3.response (optional)
missing module named Queue - imported by pip._vendor.urllib3.util.queue (conditional), pip._vendor.distlib.compat (conditional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional), pip._vendor.urllib3.contrib.pyopenssl (delayed)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional), pip._vendor.urllib3.contrib.pyopenssl (delayed, optional)
missing module named 'cryptography.hazmat' - imported by pip._vendor.urllib3.contrib.pyopenssl (top-level)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional), pip._vendor.urllib3.contrib.pyopenssl (top-level), pip._vendor.requests (conditional, optional)
missing module named 'OpenSSL.SSL' - imported by pip._vendor.urllib3.contrib.pyopenssl (top-level)
missing module named 'tornado.concurrent' - imported by pip._vendor.tenacity.tornadoweb (conditional)
missing module named tornado - imported by pip._vendor.tenacity.tornadoweb (top-level)
excluded module named __main__ - imported by pip._vendor.pkg_resources (delayed, optional)
missing module named dummy_threading - imported by requests.cookies (optional), pip._vendor.requests.cookies (optional), pip._internal.utils.logging (optional), pip._vendor.distlib.util (optional)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named Cookie - imported by pip._vendor.requests.compat (conditional)
missing module named cookielib - imported by pip._vendor.requests.compat (conditional)
missing module named urllib2 - imported by pip._vendor.requests.compat (conditional), pip._vendor.distlib.compat (conditional)
missing module named urlparse - imported by pip._vendor.requests.compat (conditional), pip._vendor.cachecontrol.compat (optional), pip._vendor.distlib.compat (conditional)
missing module named socks - imported by urllib3.contrib.socks (optional), pip._vendor.urllib3.contrib.socks (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named 'lockfile.mkdirlockfile' - imported by pip._vendor.cachecontrol.caches.file_cache (delayed, optional)
missing module named lockfile - imported by pip._vendor.cachecontrol.caches.file_cache (delayed, optional)
missing module named 'pip._vendor.requests.packages.urllib3' - imported by pip._vendor.cachecontrol.compat (optional)
missing module named cPickle - imported by pip._vendor.cachecontrol.compat (optional)
missing module named keyring - imported by pip._internal.network.auth (optional)
missing module named _abcoll - imported by pip._vendor.distlib.compat (optional)
missing module named dummy_thread - imported by pip._vendor.distlib.compat (optional)
missing module named thread - imported by pip._vendor.distlib.compat (optional)
missing module named htmlentitydefs - imported by pip._vendor.distlib.compat (conditional)
missing module named HTMLParser - imported by pip._vendor.distlib.compat (conditional)
missing module named xmlrpclib - imported by pip._vendor.distlib.compat (conditional)
missing module named httplib - imported by pip._vendor.distlib.compat (conditional)
missing module named ConfigParser - imported by pip._vendor.distlib.compat (conditional), pip._vendor.distlib._backport.sysconfig (optional)
missing module named pip.__file__ - imported by pip (top-level), pip._internal.build_env (top-level)
missing module named toml - imported by pip._vendor.pep517.compat (conditional), pydantic.v1.mypy (delayed, conditional, optional)
missing module named 'tencentcloud.tts' - imported by voice.tencent.tencent_voice (top-level)
missing module named 'tencentcloud.asr' - imported by voice.tencent.tencent_voice (top-level)
missing module named tencentcloud - imported by voice.tencent.tencent_voice (top-level)
missing module named websocket - imported by bot.xunfei.xunfei_spark_bot (top-level), voice.xunfei.xunfei_asr (top-level), voice.xunfei.xunfei_tts (top-level)
missing module named edge_tts - imported by voice.edge.edge_voice (top-level)
missing module named elevenlabs - imported by voice.elevent.elevent_voice (top-level)
missing module named langid - imported by voice.azure.azure_voice (top-level)
missing module named azure - imported by voice.azure.azure_voice (top-level)
missing module named pyttsx3 - imported by voice.pytts.pytts_voice (top-level)
missing module named cython - imported by pydantic.v1.version (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), pydantic.v1._hypothesis_plugin (optional)
missing module named tomli - imported by pydantic.mypy (delayed, conditional, optional), pydantic.v1.mypy (delayed, conditional, optional)
missing module named tomllib - imported by pydantic.mypy (delayed, conditional), pydantic.v1.mypy (delayed, conditional)
missing module named 'mypy.version' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.util' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.typevars' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.types' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.server' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.semanal' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugins' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.plugin' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.options' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.nodes' - imported by pydantic.mypy (top-level), pydantic.v1.mypy (top-level)
missing module named 'mypy.errorcodes' - imported by pydantic.v1.mypy (top-level)
missing module named dotenv - imported by pydantic.v1.env_settings (delayed, optional)
missing module named hypothesis - imported by pydantic.v1._hypothesis_plugin (top-level)
missing module named 'mypy.typeops' - imported by pydantic.mypy (top-level)
missing module named 'mypy.type_visitor' - imported by pydantic.mypy (top-level)
missing module named 'mypy.state' - imported by pydantic.mypy (top-level)
missing module named 'mypy.expandtype' - imported by pydantic.mypy (top-level)
missing module named mypy - imported by pydantic.mypy (top-level)
missing module named _typeshed - imported by pydantic_core._pydantic_core (top-level), pydantic._internal._dataclasses (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), anyio._core._tempfile (conditional), httpx._transports.wsgi (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named 'rich.pretty' - imported by pydantic._internal._core_utils (delayed)
missing module named rich - imported by pydantic._internal._core_utils (conditional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), openai.resources.beta.realtime.realtime (top-level)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named apport_python_hook - imported by exceptiongroup._formatting (conditional)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named _pytest - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by anyio._backends._asyncio (delayed, conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named asyncio.Runner - imported by asyncio (conditional), anyio._backends._asyncio (conditional)
missing module named jiter.from_json - imported by jiter (top-level), openai.lib.streaming.chat._completions (top-level)
missing module named trio - imported by httpx._transports.asgi (delayed, conditional), httpcore._synchronization (optional), httpcore._backends.trio (top-level), openai.resources.vector_stores.file_batches (delayed, conditional)
missing module named sounddevice - imported by openai._extras.sounddevice_proxy (delayed, conditional, optional)
missing module named pandas - imported by openai._extras.pandas_proxy (delayed, conditional, optional)
missing module named numpy - imported by openai._extras.numpy_proxy (delayed, conditional, optional)
missing module named 'websockets.exceptions' - imported by openai.resources.beta.realtime.realtime (delayed)
missing module named 'websockets.asyncio' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named 'websockets.sync' - imported by openai.resources.beta.realtime.realtime (delayed, conditional, optional)
missing module named 'websockets.extensions' - imported by openai.types.websocket_connection_options (conditional)
missing module named websockets - imported by openai.types.websocket_connection_options (conditional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.config' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'rich.table' - imported by httpx._main (top-level)
missing module named 'rich.syntax' - imported by httpx._main (top-level)
missing module named 'rich.progress' - imported by httpx._main (top-level)
missing module named 'rich.markup' - imported by httpx._main (top-level)
missing module named 'rich.console' - imported by httpx._main (top-level)
missing module named 'pygments.util' - imported by httpx._main (top-level)
missing module named pygments - imported by httpx._main (top-level)
missing module named click - imported by httpx._main (top-level)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named h2 - imported by urllib3.http2.connection (top-level), httpx._client (delayed, conditional, optional)
missing module named httpx_aiohttp - imported by openai._base_client (optional)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named gtts - imported by voice.google.google_voice (top-level)
missing module named speech_recognition - imported by voice.google.google_voice (top-level)
missing module named aip - imported by voice.baidu.baidu_voice (top-level)
missing module named 'openai.error' - imported by bot.chatgpt.chat_gpt_bot (top-level), bot.openai.open_ai_image (top-level), bot.openai.open_ai_bot (top-level), bot.claudeapi.claude_api_bot (top-level), bot.ali.ali_qwen_bot (top-level), bot.zhipuai.zhipuai_bot (top-level), bot.moonshot.moonshot_bot (top-level), bot.minimax.minimax_bot (top-level), bot.modelscope.modelscope_bot (top-level)
missing module named tiktoken - imported by bot.chatgpt.chat_gpt_session (delayed), bot.openai.open_ai_session (delayed)
missing module named cozepy - imported by bot.bytedance.coze_client (top-level), bot.bytedance.bytedance_coze_bot (top-level)
missing module named zhipuai - imported by bot.zhipuai.zhipu_ai_image (delayed), bot.zhipuai.zhipuai_bot (top-level)
missing module named lib - imported by bot.dify.dify_bot (top-level)
missing module named 'google.generativeai' - imported by bot.gemini.google_gemini_bot (top-level)
missing module named google - imported by bot.gemini.google_gemini_bot (top-level)
missing module named dashscope - imported by bot.dashscope.dashscope_bot (top-level)
missing module named broadscope_bailian - imported by bot.ali.ali_qwen_bot (top-level)
missing module named anthropic - imported by bot.claudeapi.claude_api_bot (top-level)
missing module named curl_cffi - imported by bot.claude.claude_ai_bot (top-level)
missing module named linkai - imported by bot.linkai.link_ai_bot (delayed, optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests (optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
excluded module named PyQt5.QtCore - imported by gui.main_window (top-level), gui.managers (top-level), gui.system_tray (top-level), gui.help_window (top-level), gui.donate_window (top-level)
excluded module named PyQt5.QtGui - imported by gui_app (delayed, optional), gui.main_window (top-level), gui.system_tray (top-level), gui.help_window (top-level), gui.donate_window (top-level)
excluded module named PyQt5.QtWidgets - imported by gui_app (delayed, optional), gui.main_window (top-level), gui.system_tray (top-level), gui.help_window (top-level), gui.donate_window (top-level)
