#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
虚拟环境打包脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    print("🚀 企业微信自动回复系统 - 虚拟环境打包")
    print("=" * 50)
    
    # 清理之前的构建
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("✅ 清理dist目录")
    
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("✅ 清理build目录")
    
    # 删除旧的spec文件
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        spec_file.unlink()
        print(f"✅ 删除spec文件: {spec_file}")
    
    # 使用简化的打包命令，避开PyQt5插件问题
    cmd = [
        'venv_build\\Scripts\\pyinstaller.exe',
        '--collect-data=ntwork',
        '--paths=.',
        '--name=WeworkAutoReply',
        '--onedir',
        '--exclude-module=PyQt5.QtWidgets',  # 暂时排除有问题的模块
        '--exclude-module=PyQt5.QtCore',
        '--exclude-module=PyQt5.QtGui',
        'main.py'
    ]
    
    print(f"📦 执行打包命令:")
    print(f"   {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, cwd='.')
        print("✅ 打包成功！")
        print("输出信息:")
        print(result.stdout)
        
        # 复制配置文件
        dist_dir = Path('dist/WeworkAutoReply')
        if dist_dir.exists():
            print("📁 复制配置文件...")
            
            files_to_copy = ['config.json', 'config-gui.json', '2.ico']
            for file in files_to_copy:
                if os.path.exists(file):
                    shutil.copy2(file, dist_dir)
                    print(f"   ✅ 复制: {file}")
            
            # 复制tmp目录
            if os.path.exists('tmp'):
                dst_tmp = dist_dir / 'tmp'
                if dst_tmp.exists():
                    shutil.rmtree(dst_tmp)
                shutil.copytree('tmp', dst_tmp)
                print("   ✅ 复制: tmp目录")
            
            print(f"\n🎉 打包完成！")
            print(f"📂 输出目录: {dist_dir.resolve()}")
            print("\n使用方法:")
            print("1. 进入 dist/WeworkAutoReply 目录")
            print("2. 双击 WeworkAutoReply.exe 启动程序")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按任意键退出...")
        sys.exit(1)
    else:
        input("\n打包完成，按任意键退出...")
