[DEBUG][2025-08-20 21:25:39][config.py:368] - [INIT] set log level to DEBUG
[INFO][2025-08-20 21:25:39][config.py:370] - [INIT] load config: {'app_name': '企业微信自动回复系统', 'app_version': '1.0.0', 'debug': True, 'window_width': 800, 'window_height': 600, 'window_title': '企业微信自动回复系统', 'minimize_to_tray': True, 'start_minimized': False, 'mock_enabled': True, 'mock_auto_reply': True, 'mock_reply_text': '测试回复消息', 'mock_reply_delay': 2, 'mock_message_interval': 10, 'log_level': 'INFO', 'log_to_file': True, 'log_file_path': 'logs/app.log', 'log_max_size': 10, 'log_backup_count': 5, 'tray_enabled': True, 'tray_show_notifications': True, 'tray_notification_duration': 3000, 'auto_reply_enabled': True, 'reply_prefix': '[自动回复] ', 'reply_suffix': '', 'service_status': 'stopped', 'last_activity': None, 'message_count': 0, 'reply_count': 0}
[INFO][2025-08-20 21:25:39][config.py:296] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-20 21:26:10][config.py:368] - [INIT] set log level to DEBUG
[INFO][2025-08-20 21:26:10][config.py:370] - [INIT] load config: {'app_name': '企业微信自动回复系统', 'app_version': '1.0.0', 'debug': True, 'window_width': 800, 'window_height': 600, 'window_title': '企业微信自动回复系统', 'minimize_to_tray': True, 'start_minimized': False, 'mock_enabled': True, 'mock_auto_reply': True, 'mock_reply_text': '测试回复消息', 'mock_reply_delay': 2, 'mock_message_interval': 10, 'log_level': 'INFO', 'log_to_file': True, 'log_file_path': 'logs/app.log', 'log_max_size': 10, 'log_backup_count': 5, 'tray_enabled': True, 'tray_show_notifications': True, 'tray_notification_duration': 3000, 'auto_reply_enabled': True, 'reply_prefix': '[自动回复] ', 'reply_suffix': '', 'service_status': 'stopped', 'last_activity': None, 'message_count': 0, 'reply_count': 0}
[INFO][2025-08-20 21:26:10][config.py:296] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-20 21:26:10][config.py:345] - [INIT] config str: {
    "app_name": "\u4f01\u4e1a\u5fae\u4fe1\u81ea\u52a8\u56de\u590d\u7cfb\u7edf",
    "app_version": "1.0.0",
    "debug": true,
    "window_width": 800,
    "window_height": 600,
    "window_title": "\u4f01\u4e1a\u5fae\u4fe1\u81ea\u52a8\u56de\u590d\u7cfb\u7edf",
    "minimize_to_tray": true,
    "start_minimized": false,
    "mock_enabled": true,
    "mock_auto_reply": true,
    "mock_reply_text": "\u6d4b\u8bd5\u56de\u590d\u6d88\u606f",
    "mock_reply_delay": 2,
    "mock_message_interval": 10,
    "log_level": "INFO",
    "log_to_file": true,
    "log_file_path": "logs/app.log",
    "log_max_size": 10,
    "log_backup_count": 5,
    "tray_enabled": true,
    "tray_show_notifications": true,
    "tray_notification_duration": 3000,
    "auto_reply_enabled": true,
    "reply_prefix": "[\u81ea\u52a8\u56de\u590d] ",
    "reply_suffix": "",
    "service_status": "stopped",
    "last_activity": null,
    "message_count": 0,
    "reply_count": 0
}
[DEBUG][2025-08-20 21:26:10][config.py:368] - [INIT] set log level to DEBUG
[INFO][2025-08-20 21:26:10][config.py:370] - [INIT] load config: {'app_name': '企业微信自动回复系统', 'app_version': '1.0.0', 'debug': True, 'window_width': 800, 'window_height': 600, 'window_title': '企业微信自动回复系统', 'minimize_to_tray': True, 'start_minimized': False, 'mock_enabled': True, 'mock_auto_reply': True, 'mock_reply_text': '测试回复消息', 'mock_reply_delay': 2, 'mock_message_interval': 10, 'log_level': 'INFO', 'log_to_file': True, 'log_file_path': 'logs/app.log', 'log_max_size': 10, 'log_backup_count': 5, 'tray_enabled': True, 'tray_show_notifications': True, 'tray_notification_duration': 3000, 'auto_reply_enabled': True, 'reply_prefix': '[自动回复] ', 'reply_suffix': '', 'service_status': 'stopped', 'last_activity': None, 'message_count': 0, 'reply_count': 0}
[INFO][2025-08-20 21:26:10][config.py:296] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-20 21:27:00][config.py:368] - [INIT] set log level to DEBUG
[INFO][2025-08-20 21:27:00][config.py:370] - [INIT] load config: {'app_name': '企业微信自动回复系统', 'app_version': '1.0.0', 'debug': True, 'window_width': 800, 'window_height': 600, 'window_title': '企业微信自动回复系统', 'minimize_to_tray': True, 'start_minimized': False, 'mock_enabled': True, 'mock_auto_reply': True, 'mock_reply_text': '测试回复消息', 'mock_reply_delay': 2, 'mock_message_interval': 10, 'log_level': 'INFO', 'log_to_file': True, 'log_file_path': 'logs/app.log', 'log_max_size': 10, 'log_backup_count': 5, 'tray_enabled': True, 'tray_show_notifications': True, 'tray_notification_duration': 3000, 'auto_reply_enabled': True, 'reply_prefix': '[自动回复] ', 'reply_suffix': '', 'service_status': 'stopped', 'last_activity': None, 'message_count': 0, 'reply_count': 0}
[INFO][2025-08-20 21:27:00][config.py:296] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-20 21:27:00][config.py:345] - [INIT] config str: {
    "app_name": "\u4f01\u4e1a\u5fae\u4fe1\u81ea\u52a8\u56de\u590d\u7cfb\u7edf",
    "app_version": "1.0.0",
    "debug": true,
    "window_width": 800,
    "window_height": 600,
    "window_title": "\u4f01\u4e1a\u5fae\u4fe1\u81ea\u52a8\u56de\u590d\u7cfb\u7edf",
    "minimize_to_tray": true,
    "start_minimized": false,
    "mock_enabled": true,
    "mock_auto_reply": true,
    "mock_reply_text": "\u6d4b\u8bd5\u56de\u590d\u6d88\u606f",
    "mock_reply_delay": 2,
    "mock_message_interval": 10,
    "log_level": "INFO",
    "log_to_file": true,
    "log_file_path": "logs/app.log",
    "log_max_size": 10,
    "log_backup_count": 5,
    "tray_enabled": true,
    "tray_show_notifications": true,
    "tray_notification_duration": 3000,
    "auto_reply_enabled": true,
    "reply_prefix": "[\u81ea\u52a8\u56de\u590d] ",
    "reply_suffix": "",
    "service_status": "stopped",
    "last_activity": null,
    "message_count": 0,
    "reply_count": 0
}
[DEBUG][2025-08-20 21:27:00][config.py:368] - [INIT] set log level to DEBUG
[INFO][2025-08-20 21:27:00][config.py:370] - [INIT] load config: {'app_name': '企业微信自动回复系统', 'app_version': '1.0.0', 'debug': True, 'window_width': 800, 'window_height': 600, 'window_title': '企业微信自动回复系统', 'minimize_to_tray': True, 'start_minimized': False, 'mock_enabled': True, 'mock_auto_reply': True, 'mock_reply_text': '测试回复消息', 'mock_reply_delay': 2, 'mock_message_interval': 10, 'log_level': 'INFO', 'log_to_file': True, 'log_file_path': 'logs/app.log', 'log_max_size': 10, 'log_backup_count': 5, 'tray_enabled': True, 'tray_show_notifications': True, 'tray_notification_duration': 3000, 'auto_reply_enabled': True, 'reply_prefix': '[自动回复] ', 'reply_suffix': '', 'service_status': 'stopped', 'last_activity': None, 'message_count': 0, 'reply_count': 0}
[INFO][2025-08-20 21:27:00][config.py:296] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-20 21:28:20][config.py:368] - [INIT] set log level to DEBUG
[INFO][2025-08-20 21:28:20][config.py:370] - [INIT] load config: {'app_name': '企业微信自动回复系统', 'app_version': '1.0.0', 'debug': True, 'window_width': 800, 'window_height': 600, 'window_title': '企业微信自动回复系统', 'minimize_to_tray': True, 'start_minimized': False, 'mock_enabled': True, 'mock_auto_reply': True, 'mock_reply_text': '测试回复消息', 'mock_reply_delay': 2, 'mock_message_interval': 10, 'log_level': 'INFO', 'log_to_file': True, 'log_file_path': 'logs/app.log', 'log_max_size': 10, 'log_backup_count': 5, 'tray_enabled': True, 'tray_show_notifications': True, 'tray_notification_duration': 3000, 'auto_reply_enabled': True, 'reply_prefix': '[自动回复] ', 'reply_suffix': '', 'service_status': 'stopped', 'last_activity': None, 'message_count': 0, 'reply_count': 0}
[INFO][2025-08-20 21:28:20][config.py:296] - [Config] User datas file not found, ignore.
[DEBUG][2025-08-20 21:28:20][audio_convert.py:10] - import pysilk failed, wechaty voice message will not be supported.
[INFO][2025-08-20 21:28:20][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 21:28:24][wework_channel.py:195] - 登录信息:>>>user_id:****************>>>>>>>>name:童树运
[INFO][2025-08-20 21:28:24][wework_channel.py:196] - 企业微信登录成功，开始获取联系人和群聊信息...
[WARNING][2025-08-20 21:28:24][wework_channel.py:173] - 获取数据失败，重试第1次······
[DEBUG][2025-08-20 21:28:28][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAMQ1pWXxQYY76TDoYWAgAMgktDx4Ac=', 'at_list': [], 'content': '好吧', 'content_type': 0, 'conversation_id': 'S:****************_1688854702657277', 'is_pc': 0, 'local_id': '39212', 'receiver': '1688854702657277', 'send_time': '1755695831', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1778805'}, 'type': 11041}
[INFO][2025-08-20 21:28:30][wework_channel.py:229] - wework程序初始化完成········
[DEBUG][2025-08-20 21:28:37][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQhJuXxQYY/7qbqpmAgAMgoBA=', 'at_list': [], 'content': '1', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 0, 'local_id': '39213', 'receiver': '****************', 'send_time': '**********', 'sender': '****************', 'sender_name': '尚二松', 'server_id': '1778816'}, 'type': 11041}
[DEBUG][2025-08-20 21:28:37][wework_channel.py:93] - 正在为单聊创建 WeworkMessage
[DEBUG][2025-08-20 21:28:37][wework_message.py:176] - login_info: {'account': '', 'acctid': '********', 'avatar': 'https://wework.qpic.cn/wwpic3az/709457_SoENIx1qR8eRJGn_1745523142/0', 'corp_id': '****************', 'document_root': 'D:\\WXWork\\****************', 'email': '', 'job_name': 'MFG作业员', 'mobile': '***********', 'nickname': '', 'pid': 24416, 'position': 'MFG作业员', 'sex': 1, 'user_id': '****************', 'username': '童树运'}
[DEBUG][2025-08-20 21:28:37][wework_message.py:224] - WeworkMessage has been successfully instantiated with message id: S:****************_****************
[DEBUG][2025-08-20 21:28:37][wework_channel.py:95] - cmsg:ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 21:28:39][wework_channel.py:100] - 准备用 WeworkChannel 处理单聊消息
[DEBUG][2025-08-20 21:28:39][wework_channel.py:248] - [WX]receive text msg: {"data": {"appinfo": "CAEQhJuXxQYY/7qbqpmAgAMgoBA=", "at_list": [], "content": "1", "content_type": 2, "conversation_id": "S:****************_****************", "is_pc": 0, "local_id": "39213", "receiver": "****************", "send_time": "**********", "sender": "****************", "sender_name": "尚二松", "server_id": "1778816"}, "type": 11041}, cmsg=ChatMessage: id=S:****************_****************, create_time=**********, ctype=TEXT, content=1, from_user_id=S:****************_****************, from_user_nickname=尚二松, to_user_id=****************, to_user_nickname=童树运, other_user_id=S:****************_****************, other_user_nickname=尚二松, is_group=False, is_at=False, actual_user_id=None, actual_user_nickname=None, at_list=None
[DEBUG][2025-08-20 21:28:39][wework_channel.py:105] - 已用 WeworkChannel 处理完单聊消息
[DEBUG][2025-08-20 21:28:39][chat_channel.py:369] - [chat_channel] consume context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000028CB3C350F0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 21:28:39][chat_channel.py:173] - [chat_channel] ready to handle context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000028CB3C350F0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************'})
[DEBUG][2025-08-20 21:28:39][chat_channel.py:195] - [chat_channel] ready to handle context: type=TEXT, content=1
[INFO][2025-08-20 21:28:39][bridge.py:80] - create bot mock for chat
[INFO][2025-08-20 21:28:39][mock_bot.py:75] - 📨 接收到新的消息 - 用户：尚二松，内容：1
[INFO][2025-08-20 21:28:39][mock_bot.py:84] - [MockBot] 开始屏蔽检查...
[INFO][2025-08-20 21:28:39][mock_bot.py:158] - [MockBot] 屏蔽列表为空，不进行屏蔽
[INFO][2025-08-20 21:28:39][mock_bot.py:86] - [MockBot] 屏蔽检查结果: False
[INFO][2025-08-20 21:28:39][mock_bot.py:117] - ✅ 已自动回复 - 用户：尚二松，回复：验证
[DEBUG][2025-08-20 21:28:39][chat_channel.py:177] - [chat_channel] ready to decorate reply: Reply(type=TEXT, content=验证)
[DEBUG][2025-08-20 21:28:39][chat_channel.py:295] - [chat_channel] ready to send reply: Reply(type=TEXT, content=验证), context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000028CB3C350F0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x0000028CA585F730>})
[DEBUG][2025-08-20 21:28:39][wework_channel.py:276] - context: Context(type=TEXT, content=1, kwargs={'isgroup': False, 'msg': <channel.wework.wework_message.WeworkMessage object at 0x0000028CB3C350F0>, 'origin_ctype': <ContextType.TEXT: 1>, 'openai_api_key': None, 'gpt_model': None, 'session_id': 'S:****************_****************', 'receiver': 'S:****************_****************', 'channel': <channel.wework.wework_channel.WeworkChannel object at 0x0000028CA585F730>})
[DEBUG][2025-08-20 21:28:39][wework_channel.py:281] - match: None
[INFO][2025-08-20 21:28:39][wework_channel.py:289] - [WX] sendMsg=Reply(type=TEXT, content=验证), receiver=S:****************_****************
[DEBUG][2025-08-20 21:28:39][chat_channel.py:323] - Worker return success, session_id = S:****************_****************
[DEBUG][2025-08-20 21:28:39][wework_channel.py:125] - 收到消息: {'data': {'appinfo': 'CAEQh5uXxQYY76TDoYWAgAMgAQ==', 'at_list': [], 'content': '验证', 'content_type': 2, 'conversation_id': 'S:****************_****************', 'is_pc': 1, 'local_id': '39214', 'receiver': '****************', 'send_time': '1755696519', 'sender': '****************', 'sender_name': '童树运', 'server_id': '1778819'}, 'type': 11041}
[DEBUG][2025-08-20 21:28:39][wework_channel.py:129] - 自己发的，直接结束
