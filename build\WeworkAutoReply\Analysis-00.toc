(['E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\main.py'],
 ['E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app',
  'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app'],
 [],
 [('E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['PyQt5.QtWidgets', 'PyQt5.QtCore', 'PyQt5.QtGui', '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('ntwork\\wc\\helper_4.0.8.6027.dat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\wc\\helper_4.0.8.6027.dat',
   'DATA')],
 '3.10.0 (tags/v3.10.0:b494f59, Oct  4 2021, 19:00:18) [MSC v.1929 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main', 'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\main.py', 'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\typing.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tempfile.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\random.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_strptime.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\datetime.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gettext.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\struct.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\inspect.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tokenize.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ast.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bisect.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shutil.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fnmatch.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\selectors.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\quopri.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\optparse.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ssl.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('gui_app',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\gui_app.py',
   'PYMODULE'),
  ('gui.main_window',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\gui\\main_window.py',
   'PYMODULE'),
  ('gui',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\gui\\__init__.py',
   'PYMODULE'),
  ('gui.donate_window',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\gui\\donate_window.py',
   'PYMODULE'),
  ('gui.help_window',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\gui\\help_window.py',
   'PYMODULE'),
  ('bot.mock.mock_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\mock\\mock_bot.py',
   'PYMODULE'),
  ('bot.mock',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\mock\\__init__.py',
   'PYMODULE'),
  ('bot', '-', 'PYMODULE'),
  ('config', 'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\config.py', 'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('common.log',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\log.py',
   'PYMODULE'),
  ('common', '-', 'PYMODULE'),
  ('common.memory',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\memory.py',
   'PYMODULE'),
  ('common.expired_dict',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\expired_dict.py',
   'PYMODULE'),
  ('common.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\utils.py',
   'PYMODULE'),
  ('common.const',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\const.py',
   'PYMODULE'),
  ('bridge.reply',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bridge\\reply.py',
   'PYMODULE'),
  ('bridge', '-', 'PYMODULE'),
  ('bridge.context',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bridge\\context.py',
   'PYMODULE'),
  ('bot.bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\bot.py',
   'PYMODULE'),
  ('gui.system_tray',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\gui\\system_tray.py',
   'PYMODULE'),
  ('gui.managers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\gui\\managers.py',
   'PYMODULE'),
  ('gui.config_loader',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\gui\\config_loader.py',
   'PYMODULE'),
  ('channel.wework.wework_channel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\channel\\wework\\wework_channel.py',
   'PYMODULE'),
  ('channel.wework', '-', 'PYMODULE'),
  ('channel', '-', 'PYMODULE'),
  ('channel.wework.run',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\channel\\wework\\run.py',
   'PYMODULE'),
  ('common.time_check',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\time_check.py',
   'PYMODULE'),
  ('common.singleton',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\singleton.py',
   'PYMODULE'),
  ('channel.wework.wework_message',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\channel\\wework\\wework_message.py',
   'PYMODULE'),
  ('ntwork.const.send_type',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\const\\send_type.py',
   'PYMODULE'),
  ('ntwork.const',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\const\\__init__.py',
   'PYMODULE'),
  ('ntwork.const.notify_type',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\const\\notify_type.py',
   'PYMODULE'),
  ('channel.chat_message',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\channel\\chat_message.py',
   'PYMODULE'),
  ('channel.chat_channel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\channel\\chat_channel.py',
   'PYMODULE'),
  ('voice.audio_convert',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\audio_convert.py',
   'PYMODULE'),
  ('voice', '-', 'PYMODULE'),
  ('wave',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wave.py',
   'PYMODULE'),
  ('chunk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\chunk.py',
   'PYMODULE'),
  ('plugins',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\plugins\\__init__.py',
   'PYMODULE'),
  ('plugins.plugin_manager',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\plugins\\plugin_manager.py',
   'PYMODULE'),
  ('common.package_manager',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\package_manager.py',
   'PYMODULE'),
  ('pip._internal.main',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\main.py',
   'PYMODULE'),
  ('pip._internal.utils.entrypoints',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\entrypoints.py',
   'PYMODULE'),
  ('pip._internal.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.appdirs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\appdirs.py',
   'PYMODULE'),
  ('pip._vendor.appdirs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\platform.py',
   'PYMODULE'),
  ('pip._vendor',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.distro',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distro.py',
   'PYMODULE'),
  ('pip._vendor.html5lib',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.serializer',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\serializer.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.optionaltags',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\optionaltags.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.base',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\base.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.sanitizer',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\sanitizer.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.whitespace',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\whitespace.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.alphabeticalattributes',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\alphabeticalattributes.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.filters.inject_meta_charset',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\filters\\inject_meta_charset.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.constants',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\constants.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.etree',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\etree.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.etree_lxml',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\etree_lxml.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders.etree',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\etree.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders.base',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\base.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.genshi',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\genshi.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.dom',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\dom.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treewalkers.base',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treewalkers\\base.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.html5parser',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\html5parser.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders.etree_lxml',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\etree_lxml.py',
   'PYMODULE'),
  ('pip._vendor.html5lib.treebuilders.dom',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\treebuilders\\dom.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._ihatexml',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\_ihatexml.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._tokenizer',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\_tokenizer.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._trie',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\_trie\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._trie.py',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\_trie\\py.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._trie._base',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\_trie\\_base.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._inputstream',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\_inputstream.py',
   'PYMODULE'),
  ('pip._vendor.chardet.universaldetector',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('pip._vendor.chardet',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.chardet.version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\version.py',
   'PYMODULE'),
  ('pip._vendor.chardet.sbcsgroupprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.sbcharsetprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.charsetprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langturkishmodel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langthaimodel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langrussianmodel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langhebrewmodel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langgreekmodel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.langbulgarianmodel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('pip._vendor.chardet.hebrewprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.mbcsgroupprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.euctwprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.mbcssm',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('pip._vendor.chardet.chardistribution',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('pip._vendor.chardet.jisfreq',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.big5freq',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\big5freq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.gb2312freq',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.euckrfreq',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.euctwfreq',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('pip._vendor.chardet.codingstatemachine',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('pip._vendor.chardet.mbcharsetprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.big5prober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\big5prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.cp949prober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.euckrprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.gb2312prober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.eucjpprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.jpcntx',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('pip._vendor.chardet.sjisprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.utf8prober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.latin1prober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.escprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\escprober.py',
   'PYMODULE'),
  ('pip._vendor.chardet.escsm',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\escsm.py',
   'PYMODULE'),
  ('pip._vendor.chardet.enums',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\enums.py',
   'PYMODULE'),
  ('pip._vendor.chardet.charsetgroupprober',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('pip._vendor.html5lib._utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\html5lib\\_utils.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('pip._vendor.webencodings',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\webencodings\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.webencodings.x_user_defined',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\webencodings\\x_user_defined.py',
   'PYMODULE'),
  ('pip._vendor.webencodings.labels',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\webencodings\\labels.py',
   'PYMODULE'),
  ('pip._vendor.msgpack',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\msgpack\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.msgpack.fallback',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\msgpack\\fallback.py',
   'PYMODULE'),
  ('pip._vendor.msgpack.ext',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\msgpack\\ext.py',
   'PYMODULE'),
  ('pip._vendor.msgpack.exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\msgpack\\exceptions.py',
   'PYMODULE'),
  ('pip._vendor.msgpack._version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\msgpack\\_version.py',
   'PYMODULE'),
  ('pip._vendor.colorama',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\colorama\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.colorama.ansitowin32',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('pip._vendor.colorama.winterm',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\colorama\\winterm.py',
   'PYMODULE'),
  ('pip._vendor.colorama.ansi',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\colorama\\ansi.py',
   'PYMODULE'),
  ('pip._vendor.colorama.initialise',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\colorama\\initialise.py',
   'PYMODULE'),
  ('pip._vendor.colorama.win32',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\colorama\\win32.py',
   'PYMODULE'),
  ('pip._vendor.six',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\six.py',
   'PYMODULE'),
  ('pip._vendor.idna',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\idna\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.idna.intranges',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\idna\\intranges.py',
   'PYMODULE'),
  ('pip._vendor.idna.core',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\idna\\core.py',
   'PYMODULE'),
  ('pip._vendor.idna.uts46data',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\idna\\uts46data.py',
   'PYMODULE'),
  ('pip._vendor.idna.idnadata',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\idna\\idnadata.py',
   'PYMODULE'),
  ('pip._vendor.idna.package_data',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\idna\\package_data.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\glob.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('pip._internal.utils._log',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\_log.py',
   'PYMODULE'),
  ('pip._internal.cli.main',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\main.py',
   'PYMODULE'),
  ('pip._internal.cli',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\__init__.py',
   'PYMODULE'),
  ('pip._internal.cli.cmdoptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\cmdoptions.py',
   'PYMODULE'),
  ('pip._internal.utils.misc',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\misc.py',
   'PYMODULE'),
  ('pip._internal.metadata.pkg_resources',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\metadata\\pkg_resources.py',
   'PYMODULE'),
  ('pip._internal.metadata.base',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\metadata\\base.py',
   'PYMODULE'),
  ('pip._internal.models.direct_url',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\direct_url.py',
   'PYMODULE'),
  ('pip._internal.models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.wheel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\wheel.py',
   'PYMODULE'),
  ('pip._internal.utils.pkg_resources',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\pkg_resources.py',
   'PYMODULE'),
  ('pip._internal.utils.packaging',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\packaging.py',
   'PYMODULE'),
  ('pip._vendor.packaging.specifiers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pip._vendor.packaging',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.packaging._musllinux',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pip._vendor.packaging._manylinux',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pip._vendor.packaging.__about__',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pip._vendor.packaging.version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pip._vendor.packaging._structures',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pip._vendor.packaging.requirements',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pip._vendor.packaging.markers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pip._vendor.pyparsing',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pdb.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tty.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\codeop.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\cmd.py',
   'PYMODULE'),
  ('pip._internal.metadata',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\metadata\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.virtualenv',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\virtualenv.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\site.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pip._internal.utils.compat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\compat.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\response.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.six',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.ssl_match_hostname',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\ssl_match_hostname\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.ssl_match_hostname._implementation',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\ssl_match_hostname\\_implementation.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ipaddress.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\connection.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('pip._vendor.urllib3._collections',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\_collections.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.poolmanager',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.request',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\request.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.filepost',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\filepost.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.fields',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\fields.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.connectionpool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.queue',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('pip._vendor.urllib3._version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\_version.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.wait',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.url',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.timeout',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.ssl_',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.ssltransport',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.retry',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.request',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.util.connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib._appengine_environ',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib.pyopenssl',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.backports.makefile',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.packages.backports',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib.securetransport',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\securetransport.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib._securetransport.low_level',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\low_level.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib._securetransport',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib._securetransport.bindings',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\_securetransport\\bindings.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.retry',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\retry.py',
   'PYMODULE'),
  ('pip._vendor.tenacity',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.tornadoweb',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\tornadoweb.py',
   'PYMODULE'),
  ('pip._vendor.tenacity._asyncio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\_asyncio.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.before_sleep',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\before_sleep.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.after',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\after.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.before',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\before.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.wait',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\wait.py',
   'PYMODULE'),
  ('pip._vendor.tenacity._utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\_utils.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.stop',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\stop.py',
   'PYMODULE'),
  ('pip._vendor.tenacity.nap',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tenacity\\nap.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.pkg_resources',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.pkg_resources.py31compat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\pkg_resources\\py31compat.py',
   'PYMODULE'),
  ('imp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\imp.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\plistlib.py',
   'PYMODULE'),
  ('pip._internal.utils.hashes',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\hashes.py',
   'PYMODULE'),
  ('pip._internal.models.target_python',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\target_python.py',
   'PYMODULE'),
  ('pip._internal.utils.compatibility_tags',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\compatibility_tags.py',
   'PYMODULE'),
  ('pip._vendor.packaging.tags',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pip._internal.models.index',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\index.py',
   'PYMODULE'),
  ('pip._internal.models.format_control',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\format_control.py',
   'PYMODULE'),
  ('pip._internal.locations',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\locations\\__init__.py',
   'PYMODULE'),
  ('distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\cgi.py',
   'PYMODULE'),
  ('distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\configparser.py',
   'PYMODULE'),
  ('distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('pip._internal.locations.base',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\locations\\base.py',
   'PYMODULE'),
  ('pip._internal.locations._sysconfig',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\locations\\_sysconfig.py',
   'PYMODULE'),
  ('pip._internal.locations._distutils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\locations\\_distutils.py',
   'PYMODULE'),
  ('pip._internal.models.scheme',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\scheme.py',
   'PYMODULE'),
  ('pip._internal.cli.progress_bars',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\progress_bars.py',
   'PYMODULE'),
  ('pip._internal.utils.logging',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\logging.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\smtplib.py',
   'PYMODULE'),
  ('pip._vendor.progress.spinner',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\progress\\spinner.py',
   'PYMODULE'),
  ('pip._vendor.progress',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\progress\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.progress.bar',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\progress\\bar.py',
   'PYMODULE'),
  ('pip._internal.cli.parser',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\parser.py',
   'PYMODULE'),
  ('pip._internal.configuration',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\configuration.py',
   'PYMODULE'),
  ('pip._internal.cli.status_codes',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\status_codes.py',
   'PYMODULE'),
  ('pip._vendor.packaging.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pip._internal.utils.deprecation',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\deprecation.py',
   'PYMODULE'),
  ('pip._internal.exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\exceptions.py',
   'PYMODULE'),
  ('pip._internal.req.req_install',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\req\\req_install.py',
   'PYMODULE'),
  ('pip._internal.req',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\req\\__init__.py',
   'PYMODULE'),
  ('pip._internal.req.req_set',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\req\\req_set.py',
   'PYMODULE'),
  ('pip._internal.models.wheel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\wheel.py',
   'PYMODULE'),
  ('pip._internal.req.req_file',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\req\\req_file.py',
   'PYMODULE'),
  ('pip._internal.index.package_finder',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\index\\package_finder.py',
   'PYMODULE'),
  ('pip._internal.index',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\index\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.unpacking',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\unpacking.py',
   'PYMODULE'),
  ('pip._internal.utils.filetypes',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\filetypes.py',
   'PYMODULE'),
  ('pip._internal.models.selection_prefs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\selection_prefs.py',
   'PYMODULE'),
  ('pip._internal.models.candidate',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\candidate.py',
   'PYMODULE'),
  ('pip._internal.utils.models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\models.py',
   'PYMODULE'),
  ('pip._internal.index.collector',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\index\\collector.py',
   'PYMODULE'),
  ('pip._internal.index.sources',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\index\\sources.py',
   'PYMODULE'),
  ('pip._vendor.requests.exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\exceptions.py',
   'PYMODULE'),
  ('pip._vendor.requests',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.requests.status_codes',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\status_codes.py',
   'PYMODULE'),
  ('pip._vendor.requests.structures',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\structures.py',
   'PYMODULE'),
  ('pip._vendor.requests.compat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('pip._vendor.requests.api',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\api.py',
   'PYMODULE'),
  ('pip._vendor.requests.sessions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\sessions.py',
   'PYMODULE'),
  ('pip._vendor.requests.adapters',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\adapters.py',
   'PYMODULE'),
  ('pip._vendor.urllib3.contrib.socks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('pip._vendor.requests._internal_utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('pip._vendor.requests.hooks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\hooks.py',
   'PYMODULE'),
  ('pip._vendor.requests.cookies',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\cookies.py',
   'PYMODULE'),
  ('pip._vendor.requests.auth',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\auth.py',
   'PYMODULE'),
  ('pip._vendor.requests.packages',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\packages.py',
   'PYMODULE'),
  ('pip._vendor.requests.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\utils.py',
   'PYMODULE'),
  ('pip._vendor.requests.certs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\certs.py',
   'PYMODULE'),
  ('pip._vendor.certifi',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\certifi\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.certifi.core',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('pip._vendor.requests.__version__',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\__version__.py',
   'PYMODULE'),
  ('pip._internal.utils.urls',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\urls.py',
   'PYMODULE'),
  ('pip._internal.utils.encoding',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\encoding.py',
   'PYMODULE'),
  ('pip._internal.network.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\network\\utils.py',
   'PYMODULE'),
  ('pip._internal.network',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\network\\__init__.py',
   'PYMODULE'),
  ('pip._internal.network.session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\network\\session.py',
   'PYMODULE'),
  ('pip._internal.utils.glibc',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\glibc.py',
   'PYMODULE'),
  ('pip._internal.network.cache',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\network\\cache.py',
   'PYMODULE'),
  ('pip._internal.utils.filesystem',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\filesystem.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.caches',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.caches.redis_cache',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\redis_cache.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.caches.file_cache',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\caches\\file_cache.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.controller',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\controller.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.serialize',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\serialize.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.compat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\compat.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.cache',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\cache.py',
   'PYMODULE'),
  ('pip._internal.network.auth',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\network\\auth.py',
   'PYMODULE'),
  ('pip._internal.vcs.versioncontrol',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\vcs\\versioncontrol.py',
   'PYMODULE'),
  ('pip._internal.utils.subprocess',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\subprocess.py',
   'PYMODULE'),
  ('pip._internal.cli.spinners',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\spinners.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.adapter',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\adapter.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.filewrapper',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\filewrapper.py',
   'PYMODULE'),
  ('pip._vendor.cachecontrol.wrapper',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\cachecontrol\\wrapper.py',
   'PYMODULE'),
  ('pip._internal.models.search_scope',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\search_scope.py',
   'PYMODULE'),
  ('pip._internal.vcs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\vcs\\__init__.py',
   'PYMODULE'),
  ('pip._internal.vcs.subversion',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\vcs\\subversion.py',
   'PYMODULE'),
  ('pip._internal.vcs.mercurial',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\vcs\\mercurial.py',
   'PYMODULE'),
  ('pip._internal.vcs.git',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\vcs\\git.py',
   'PYMODULE'),
  ('pip._internal.vcs.bazaar',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\vcs\\bazaar.py',
   'PYMODULE'),
  ('pip._internal.utils.temp_dir',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\temp_dir.py',
   'PYMODULE'),
  ('pip._internal.utils.direct_url_helpers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\direct_url_helpers.py',
   'PYMODULE'),
  ('pip._internal.req.req_uninstall',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\req\\req_uninstall.py',
   'PYMODULE'),
  ('pip._internal.pyproject',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\pyproject.py',
   'PYMODULE'),
  ('pip._vendor.tomli',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.tomli._parser',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('pip._vendor.tomli._re',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('pip._internal.operations.install.wheel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\operations\\install\\wheel.py',
   'PYMODULE'),
  ('pip._internal.operations.install',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\operations\\install\\__init__.py',
   'PYMODULE'),
  ('pip._internal.operations',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\operations\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.distlib.util',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distlib\\util.py',
   'PYMODULE'),
  ('pip._vendor.distlib.compat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distlib\\compat.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\config.py',
   'PYMODULE'),
  ('pip._vendor.distlib._backport.sysconfig',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distlib\\_backport\\sysconfig.py',
   'PYMODULE'),
  ('pip._vendor.distlib.resources',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distlib\\resources.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_markupbase.py',
   'PYMODULE'),
  ('pip._vendor.distlib._backport.shutil',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distlib\\_backport\\shutil.py',
   'PYMODULE'),
  ('pip._vendor.distlib._backport.tarfile',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distlib\\_backport\\tarfile.py',
   'PYMODULE'),
  ('pip._vendor.distlib._backport',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distlib\\_backport\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.distlib',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distlib\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.distlib.scripts',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\distlib\\scripts.py',
   'PYMODULE'),
  ('compileall',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\compileall.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('filecmp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\filecmp.py',
   'PYMODULE'),
  ('pip._internal.operations.install.legacy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\operations\\install\\legacy.py',
   'PYMODULE'),
  ('pip._internal.utils.setuptools_build',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\setuptools_build.py',
   'PYMODULE'),
  ('pip._internal.operations.install.editable_legacy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\operations\\install\\editable_legacy.py',
   'PYMODULE'),
  ('pip._internal.operations.build.metadata_legacy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\operations\\build\\metadata_legacy.py',
   'PYMODULE'),
  ('pip._internal.operations.build',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\operations\\build\\__init__.py',
   'PYMODULE'),
  ('pip._internal.operations.build.metadata',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\operations\\build\\metadata.py',
   'PYMODULE'),
  ('pip._internal.models.link',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\models\\link.py',
   'PYMODULE'),
  ('pip._internal.build_env',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\build_env.py',
   'PYMODULE'),
  ('pip._vendor.pep517.wrappers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\pep517\\wrappers.py',
   'PYMODULE'),
  ('pip._vendor.pep517.in_process',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\pep517\\in_process\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.pep517.compat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\pep517\\compat.py',
   'PYMODULE'),
  ('pip._vendor.pep517',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\pep517\\__init__.py',
   'PYMODULE'),
  ('pip._vendor.requests.models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_vendor\\requests\\models.py',
   'PYMODULE'),
  ('pip._internal.commands',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\commands\\__init__.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\difflib.py',
   'PYMODULE'),
  ('pip._internal.cli.base_command',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\base_command.py',
   'PYMODULE'),
  ('pip._internal.cli.command_context',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\command_context.py',
   'PYMODULE'),
  ('pip._internal.cli.main_parser',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\main_parser.py',
   'PYMODULE'),
  ('pip._internal.cli.autocompletion',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\cli\\autocompletion.py',
   'PYMODULE'),
  ('pip._internal',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\__init__.py',
   'PYMODULE'),
  ('pip._internal.utils.inject_securetransport',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\_internal\\utils\\inject_securetransport.py',
   'PYMODULE'),
  ('pip',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pip\\__init__.py',
   'PYMODULE'),
  ('common.sorted_dict',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\sorted_dict.py',
   'PYMODULE'),
  ('plugins.plugin',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\plugins\\plugin.py',
   'PYMODULE'),
  ('plugins.event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\plugins\\event.py',
   'PYMODULE'),
  ('common.dequeue',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\dequeue.py',
   'PYMODULE'),
  ('channel.channel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\channel\\channel.py',
   'PYMODULE'),
  ('bridge.bridge',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bridge\\bridge.py',
   'PYMODULE'),
  ('voice.factory',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\factory.py',
   'PYMODULE'),
  ('voice.tencent.tencent_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\tencent\\tencent_voice.py',
   'PYMODULE'),
  ('voice.tencent', '-', 'PYMODULE'),
  ('common.tmp_dir',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\tmp_dir.py',
   'PYMODULE'),
  ('voice.voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\voice.py',
   'PYMODULE'),
  ('voice.dify.dify_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\dify\\dify_voice.py',
   'PYMODULE'),
  ('voice.dify', '-', 'PYMODULE'),
  ('voice.xunfei.xunfei_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\xunfei\\xunfei_voice.py',
   'PYMODULE'),
  ('voice.xunfei', '-', 'PYMODULE'),
  ('voice.xunfei.xunfei_tts',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\xunfei\\xunfei_tts.py',
   'PYMODULE'),
  ('wsgiref.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wsgiref\\handlers.py',
   'PYMODULE'),
  ('wsgiref',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('wsgiref.headers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wsgiref\\headers.py',
   'PYMODULE'),
  ('wsgiref.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\wsgiref\\util.py',
   'PYMODULE'),
  ('voice.xunfei.xunfei_asr',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\xunfei\\xunfei_asr.py',
   'PYMODULE'),
  ('voice.edge.edge_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\edge\\edge_voice.py',
   'PYMODULE'),
  ('voice.edge', '-', 'PYMODULE'),
  ('voice.ali.ali_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\ali\\ali_voice.py',
   'PYMODULE'),
  ('voice.ali', '-', 'PYMODULE'),
  ('voice.ali.ali_api',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\ali\\ali_api.py',
   'PYMODULE'),
  ('voice.linkai.linkai_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\linkai\\linkai_voice.py',
   'PYMODULE'),
  ('voice.linkai', '-', 'PYMODULE'),
  ('voice.elevent.elevent_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\elevent\\elevent_voice.py',
   'PYMODULE'),
  ('voice.elevent', '-', 'PYMODULE'),
  ('voice.azure.azure_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\azure\\azure_voice.py',
   'PYMODULE'),
  ('voice.azure', '-', 'PYMODULE'),
  ('voice.pytts.pytts_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\pytts\\pytts_voice.py',
   'PYMODULE'),
  ('voice.pytts', '-', 'PYMODULE'),
  ('voice.openai.openai_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\openai\\openai_voice.py',
   'PYMODULE'),
  ('voice.openai', '-', 'PYMODULE'),
  ('openai',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\__init__.py',
   'PYMODULE'),
  ('openai._module_client',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_module_client.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.vector_stores',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\vector_stores\\vector_stores.py',
   'PYMODULE'),
  ('openai.resources.vector_stores',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\vector_stores\\__init__.py',
   'PYMODULE'),
  ('openai.resources',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\__init__.py',
   'PYMODULE'),
  ('openai.resources.responses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.resources.responses.input_items',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\responses\\input_items.py',
   'PYMODULE'),
  ('openai.types.responses.response_includable',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_includable.py',
   'PYMODULE'),
  ('openai.types.responses.response_item',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_output_item',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_item',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_screenshot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_screenshot.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_item',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_file_search_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_web_search',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_function_web_search.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_item',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_message_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_content_list',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_message_content_list.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_content',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_content.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_image',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_image.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_text',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_text.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_file',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_file.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_message',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_message.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_refusal',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_refusal.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_text.py',
   'PYMODULE'),
  ('openai.types.responses.input_item_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\input_item_list_params.py',
   'PYMODULE'),
  ('openai.types.responses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_output_screenshot_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_output_screenshot_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_interpreting_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_interpreting_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_in_progress_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_code_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_code_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_call_code_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_call_code_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_json_schema_config_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_format_text_json_schema_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_call_arguments_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_function_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_text_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_part_added_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_part_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text_annotation_added_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_text_annotation_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_partial_image_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_call_arguments_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_function_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_in_progress_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_file_search_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_input_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_input_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_in_progress_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_web_search_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_text_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_summary_part_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_reasoning_summary_part_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_input_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_input_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_in_progress_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_message_content_list_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_message_content_list_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_in_progress_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_searching_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_file_search_call_searching_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_call_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_file_search_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_code_interpreter_tool_call_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_code_interpreter_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_searching_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_web_search_call_searching_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_web_search_call_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_web_search_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_generating_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_generating_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_arguments_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_mcp_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_image_gen_call_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_image_gen_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_json_schema_config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_format_text_json_schema_config.py',
   'PYMODULE'),
  ('pydantic',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic.validators',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\colorsys.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.typing',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.tools',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.schema',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.parse',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.json',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.generics',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('annotated_types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('pydantic.experimental.arguments_schema',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\experimental\\arguments_schema.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.color',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('typing_inspection.typing_objects',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\typing_inspection\\typing_objects.py',
   'PYMODULE'),
  ('typing_inspection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\typing_inspection\\__init__.py',
   'PYMODULE'),
  ('zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('typing_inspection.introspection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\typing_inspection\\introspection.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._schema_gather',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_schema_gather.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.networks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.main',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.fields',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.errors',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydantic_core',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic.version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic._migration',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic.types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_arguments_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_mcp_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_output_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_output_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_transcript_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_audio_transcript_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_list_tools_failed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_mcp_list_tools_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_file_search_tool_call_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_file_search_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_transcript_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_audio_transcript_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_text_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_reasoning_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_in_progress_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_mcp_call_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_text_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_reasoning_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_web_search_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_function_web_search_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_mcp_call_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_function_tool_call_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_function_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_config_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_format_text_config_param.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_json_object',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\response_format_json_object.py',
   'PYMODULE'),
  ('openai.types.shared_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\__init__.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_json_schema',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\response_format_json_schema.py',
   'PYMODULE'),
  ('openai.types.shared_params.custom_tool_input_format',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\custom_tool_input_format.py',
   'PYMODULE'),
  ('openai.types.shared_params.function_parameters',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\function_parameters.py',
   'PYMODULE'),
  ('openai.types.shared_params.function_definition',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\function_definition.py',
   'PYMODULE'),
  ('openai.types.shared_params.comparison_filter',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\comparison_filter.py',
   'PYMODULE'),
  ('openai.types.shared_params.reasoning_effort',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.shared_params.responses_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\responses_model.py',
   'PYMODULE'),
  ('openai.types.shared.chat_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\chat_model.py',
   'PYMODULE'),
  ('openai.types.shared',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\__init__.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_text_grammar',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\response_format_text_grammar.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_text_python',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\response_format_text_python.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_json_schema',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\response_format_json_schema.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_json_object',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\response_format_json_object.py',
   'PYMODULE'),
  ('openai.types.shared.custom_tool_input_format',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\custom_tool_input_format.py',
   'PYMODULE'),
  ('openai.types.shared.response_format_text',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\response_format_text.py',
   'PYMODULE'),
  ('openai.types.shared.function_parameters',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\function_parameters.py',
   'PYMODULE'),
  ('openai.types.shared.function_definition',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\function_definition.py',
   'PYMODULE'),
  ('openai.types.shared.comparison_filter',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\comparison_filter.py',
   'PYMODULE'),
  ('openai.types.shared.reasoning_effort',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.shared.responses_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\responses_model.py',
   'PYMODULE'),
  ('openai.types.shared.compound_filter',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\compound_filter.py',
   'PYMODULE'),
  ('openai.types.shared.error_object',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\error_object.py',
   'PYMODULE'),
  ('openai.types.shared.all_models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\all_models.py',
   'PYMODULE'),
  ('openai.types.shared.reasoning',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\reasoning.py',
   'PYMODULE'),
  ('openai.types.shared.metadata',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared\\metadata.py',
   'PYMODULE'),
  ('openai.types.shared_params.compound_filter',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\compound_filter.py',
   'PYMODULE'),
  ('openai.types.shared_params.chat_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\chat_model.py',
   'PYMODULE'),
  ('openai.types.shared_params.reasoning',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\reasoning.py',
   'PYMODULE'),
  ('openai.types.shared_params.response_format_text',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\response_format_text.py',
   'PYMODULE'),
  ('openai.types.responses.response_content_part_added_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_content_part_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_computer_tool_call_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_computer_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item_added_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_item_added_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_output',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_output.py',
   'PYMODULE'),
  ('openai.types.responses.response_content_part_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_content_part_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_item_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_mcp_call_failed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_mcp_call_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_item_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_reasoning_item_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_refusal_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_refusal_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_message_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_message_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_refusal_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_refusal_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_content_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_content_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_refusal_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_refusal_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_format_text_config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_format_text_config.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_function_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_function_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_config_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_text_config_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_text_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_text_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_image_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_image_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_in_progress_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_in_progress_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_audio_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_allowed_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_allowed_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_text_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_text_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_item_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_item_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_file_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_file_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_incomplete_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_incomplete_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_custom_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_custom_tool_call.py',
   'PYMODULE'),
  ('openai.types.responses.response_audio_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_audio_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_custom_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_custom_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_text_done_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_retrieve_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.responses.response_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_completed_event.py',
   'PYMODULE'),
  ('openai.types.responses.easy_input_message_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\easy_input_message_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_types_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_types_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_reasoning_item',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_reasoning_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_created_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_created_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_create_params.py',
   'PYMODULE'),
  ('openai.types.responses.file_search_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\file_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.web_search_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\web_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_mcp_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_mcp_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_stream_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_stream_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_queued_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_queued_event.py',
   'PYMODULE'),
  ('openai.types.responses.response_prompt_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_prompt_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_failed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_failed_event.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_function',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_function.py',
   'PYMODULE'),
  ('openai.types.responses.response_text_config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_text_config.py',
   'PYMODULE'),
  ('openai.types.responses.response_output_item',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_output_item.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_param.py',
   'PYMODULE'),
  ('openai.types.responses.response_error_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_error_event.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_options',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_options.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_allowed',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_allowed.py',
   'PYMODULE'),
  ('openai.types.responses.response_input_item',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_input_item.py',
   'PYMODULE'),
  ('openai.types.responses.function_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\function_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.computer_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\computer_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_custom',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_custom.py',
   'PYMODULE'),
  ('openai.types.responses.response_item_list',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_item_list.py',
   'PYMODULE'),
  ('openai.types.responses.easy_input_message',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\easy_input_message.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_types.py',
   'PYMODULE'),
  ('openai.types.responses.custom_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\custom_tool_param.py',
   'PYMODULE'),
  ('openai.types.responses.file_search_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\file_search_tool.py',
   'PYMODULE'),
  ('openai.types.responses.web_search_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\web_search_tool.py',
   'PYMODULE'),
  ('openai.types.responses.tool_choice_mcp',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_choice_mcp.py',
   'PYMODULE'),
  ('openai.types.responses.response_status',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_status.py',
   'PYMODULE'),
  ('openai.types.responses.response_prompt',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_prompt.py',
   'PYMODULE'),
  ('openai.types.responses.parsed_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\parsed_response.py',
   'PYMODULE'),
  ('openai._utils._transform',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\_transform.py',
   'PYMODULE'),
  ('openai._utils._typing',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\_typing.py',
   'PYMODULE'),
  ('openai._files',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_files.py',
   'PYMODULE'),
  ('openai._utils._utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\_utils.py',
   'PYMODULE'),
  ('sniffio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('anyio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('exceptiongroup',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\exceptiongroup\\__init__.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\exceptiongroup\\_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\exceptiongroup\\_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\exceptiongroup\\_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\exceptiongroup\\_version.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\exceptiongroup\\_catch.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio._core',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio.abc',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('anyio._backends',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_tempfile.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('idna',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('anyio._core._contextmanagers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\anyio\\_core\\_contextmanagers.py',
   'PYMODULE'),
  ('openai.types.responses.response_usage',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_usage.py',
   'PYMODULE'),
  ('openai.types.responses.response_error',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response_error.py',
   'PYMODULE'),
  ('openai.types.responses.function_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\function_tool.py',
   'PYMODULE'),
  ('openai.types.responses.computer_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\computer_tool.py',
   'PYMODULE'),
  ('openai.types.responses.custom_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\custom_tool.py',
   'PYMODULE'),
  ('openai.types.responses.tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool_param.py',
   'PYMODULE'),
  ('openai.types.chat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_function_tool_call_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_message_function_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_named_tool_choice_custom_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_named_tool_choice_custom_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_custom_tool_call_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_message_custom_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_input_audio_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_input_audio_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call_union_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call_union_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_function_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_message_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_call_option_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_function_call_option_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_refusal_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_refusal_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_allowed_tool_choice_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_allowed_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_choice_option_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_choice_option_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_prediction_content_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_prediction_content_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_custom_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_message_custom_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_image_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_image_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_named_tool_choice_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_named_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_developer_message_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_developer_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_text_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_text_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_assistant_message_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_assistant_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_message_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_function_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_system_message_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_system_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_stream_options_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_stream_options_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_function_tool_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_allowed_tools_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_allowed_tools_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_user_message_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_user_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_message_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_image',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_image.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_message_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_custom_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_custom_tool_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_content_part_text',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_content_part_text.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_union_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_union_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_reasoning_effort',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_reasoning_effort.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_token_logprob',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_token_logprob.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_store_message',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_store_message.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_message_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_function_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_function_tool.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_audio_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_audio_param.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_tool_param.py',
   'PYMODULE'),
  ('openai.types.chat.parsed_function_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\parsed_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.chat.completion_update_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\completion_update_params.py',
   'PYMODULE'),
  ('openai.types.chat.completion_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\completion_create_params.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_modality',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_modality.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_message',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_message.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_deleted',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_deleted.py',
   'PYMODULE'),
  ('openai.types.chat.parsed_chat_completion',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\parsed_chat_completion.py',
   'PYMODULE'),
  ('openai.types.chat.completion_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\completion_list_params.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_chunk',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_chunk.py',
   'PYMODULE'),
  ('openai.types.completion_usage',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\completion_usage.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_audio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_audio.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion_role',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion_role.py',
   'PYMODULE'),
  ('openai.types.chat.chat_completion',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\chat_completion.py',
   'PYMODULE'),
  ('openai.types.responses.response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\response.py',
   'PYMODULE'),
  ('openai.types.responses.tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\responses\\tool.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints.permissions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\permissions.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_retrieve_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_delete_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_delete_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_create_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_create_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_retrieve_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints.permission_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\permission_create_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.checkpoints',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\__init__.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_wandb_integration_object',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_wandb_integration_object.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_hyperparameters_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_wandb_integration',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_wandb_integration.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_hyperparameters_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\supervised_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_hyperparameters',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_integration',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_integration.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_hyperparameters',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\supervised_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_method_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_method_param.py',
   'PYMODULE'),
  ('openai.types.graders.text_similarity_grader_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\text_similarity_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\__init__.py',
   'PYMODULE'),
  ('openai.types.graders.label_model_grader_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\label_model_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.text_similarity_grader',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\text_similarity_grader.py',
   'PYMODULE'),
  ('openai.types.graders.string_check_grader',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\string_check_grader.py',
   'PYMODULE'),
  ('openai.types.graders.score_model_grader',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\score_model_grader.py',
   'PYMODULE'),
  ('openai.types.graders.label_model_grader',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\label_model_grader.py',
   'PYMODULE'),
  ('openai.types.graders.python_grader',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\python_grader.py',
   'PYMODULE'),
  ('openai.types.graders.multi_grader',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\multi_grader.py',
   'PYMODULE'),
  ('openai.types.graders.string_check_grader_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\string_check_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.score_model_grader_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\score_model_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.python_grader_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\python_grader_param.py',
   'PYMODULE'),
  ('openai.types.graders.multi_grader_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\graders\\multi_grader_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_hyperparameters_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\dpo_hyperparameters_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_method_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\supervised_method_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_list_events_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\job_list_events_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job_event.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.reinforcement_method',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\reinforcement_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_hyperparameters',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\dpo_hyperparameters.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.supervised_method',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\supervised_method.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\job_create_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_method_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\dpo_method_param.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.job_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\job_list_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.fine_tuning_job',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\fine_tuning_job.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.dpo_method',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\dpo_method.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.checkpoints.checkpoints',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\checkpoints\\checkpoints.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha.graders',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\graders.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_validate_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_validate_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_run_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_run_response.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_validate_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_validate_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha.grader_run_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\grader_run_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.alpha',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\alpha\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.alpha.alpha',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\alpha.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs.checkpoints',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\checkpoints.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs.fine_tuning_job_checkpoint',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\fine_tuning_job_checkpoint.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs.checkpoint_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\checkpoint_list_params.py',
   'PYMODULE'),
  ('openai.types.fine_tuning.jobs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\fine_tuning\\jobs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.jobs.jobs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\jobs.py',
   'PYMODULE'),
  ('openai.resources.containers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\containers\\__init__.py',
   'PYMODULE'),
  ('openai.resources.containers.files',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\containers\\files\\__init__.py',
   'PYMODULE'),
  ('openai.resources.containers.files.content',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\containers\\files\\content.py',
   'PYMODULE'),
  ('openai.resources.containers.files.files',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\containers\\files\\files.py',
   'PYMODULE'),
  ('openai.types.containers.file_retrieve_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\containers\\file_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.containers.file_create_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\containers\\file_create_response.py',
   'PYMODULE'),
  ('openai.types.containers.file_list_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\containers\\file_list_response.py',
   'PYMODULE'),
  ('openai.types.containers.file_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\containers\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.containers.file_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\containers\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.containers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\containers\\__init__.py',
   'PYMODULE'),
  ('openai.resources.uploads',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\uploads\\__init__.py',
   'PYMODULE'),
  ('openai.resources.uploads.parts',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\uploads\\parts.py',
   'PYMODULE'),
  ('openai.types.uploads.upload_part',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\uploads\\upload_part.py',
   'PYMODULE'),
  ('openai.types.uploads.part_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\uploads\\part_create_params.py',
   'PYMODULE'),
  ('openai.types.uploads',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\uploads\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\evals\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals.runs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\evals\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals.runs.output_items',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\evals\\runs\\output_items.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_retrieve_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\runs\\output_item_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.evals.eval_api_error',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\eval_api_error.py',
   'PYMODULE'),
  ('openai.types.evals',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\__init__.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_completions_run_data_source_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\create_eval_completions_run_data_source_param.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_jsonl_run_data_source_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\create_eval_jsonl_run_data_source_param.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_completions_run_data_source',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\create_eval_completions_run_data_source.py',
   'PYMODULE'),
  ('openai.types.evals.create_eval_jsonl_run_data_source',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\create_eval_jsonl_run_data_source.py',
   'PYMODULE'),
  ('openai.types.evals.run_retrieve_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\run_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_delete_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\run_delete_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_create_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\run_create_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_cancel_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\run_cancel_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_list_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\run_list_response.py',
   'PYMODULE'),
  ('openai.types.evals.run_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\run_create_params.py',
   'PYMODULE'),
  ('openai.types.evals.run_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\run_list_params.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_list_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\runs\\output_item_list_response.py',
   'PYMODULE'),
  ('openai.types.evals.runs.output_item_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\runs\\output_item_list_params.py',
   'PYMODULE'),
  ('openai.types.evals.runs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\evals\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.resources.evals.runs.runs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\evals\\runs\\runs.py',
   'PYMODULE'),
  ('openai.resources.audio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\audio\\__init__.py',
   'PYMODULE'),
  ('openai.resources.audio.transcriptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\audio\\transcriptions.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_create_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription_create_response.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_stream_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription_stream_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_text_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_text_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription_text_done_event.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_verbose',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription_verbose.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_segment',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription_segment.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_word',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription_word.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_include',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription_include.py',
   'PYMODULE'),
  ('openai.types.audio_response_format',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio_response_format.py',
   'PYMODULE'),
  ('openai.types.audio.transcription',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription.py',
   'PYMODULE'),
  ('openai.types.audio_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio_model.py',
   'PYMODULE'),
  ('openai.types.audio.transcription_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\transcription_create_params.py',
   'PYMODULE'),
  ('openai.types.audio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\__init__.py',
   'PYMODULE'),
  ('openai.types.audio.translation_create_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\translation_create_response.py',
   'PYMODULE'),
  ('openai.types.audio.translation_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\translation_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.speech_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\speech_create_params.py',
   'PYMODULE'),
  ('openai.types.audio.translation_verbose',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\translation_verbose.py',
   'PYMODULE'),
  ('openai.types.audio.speech_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\speech_model.py',
   'PYMODULE'),
  ('openai.types.audio.translation',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\audio\\translation.py',
   'PYMODULE'),
  ('openai._streaming',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_streaming.py',
   'PYMODULE'),
  ('openai.resources.audio.translations',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\audio\\translations.py',
   'PYMODULE'),
  ('openai.resources.audio.speech',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\audio\\speech.py',
   'PYMODULE'),
  ('openai.resources.chat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.resources.chat.completions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\chat\\completions\\__init__.py',
   'PYMODULE'),
  ('openai.resources.chat.completions.completions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\chat\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._completions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\chat\\_completions.py',
   'PYMODULE'),
  ('openai.lib.streaming._deltas',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\_deltas.py',
   'PYMODULE'),
  ('jiter',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\jiter\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._events',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\chat\\_events.py',
   'PYMODULE'),
  ('openai.lib.streaming.chat._types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\chat\\_types.py',
   'PYMODULE'),
  ('openai.lib._parsing',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\_parsing\\__init__.py',
   'PYMODULE'),
  ('openai.lib._parsing._completions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\_parsing\\_completions.py',
   'PYMODULE'),
  ('openai.lib._pydantic',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\_pydantic.py',
   'PYMODULE'),
  ('openai.lib._tools',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\_tools.py',
   'PYMODULE'),
  ('openai.resources.chat.completions.messages',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\chat\\completions\\messages.py',
   'PYMODULE'),
  ('openai.types.chat.completions.message_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\completions\\message_list_params.py',
   'PYMODULE'),
  ('openai.types.chat.completions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat\\completions\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.assistants',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\assistants.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_response_format_option_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_response_format_option_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.code_interpreter_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\code_interpreter_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.file_search_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\file_search_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.function_tool_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\function_tool_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_deleted',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.assistant',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_response_format_option',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_response_format_option.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_tool.py',
   'PYMODULE'),
  ('openai.types.beta.code_interpreter_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\code_interpreter_tool.py',
   'PYMODULE'),
  ('openai.types.beta.file_search_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\file_search_tool.py',
   'PYMODULE'),
  ('openai.types.beta.function_tool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\function_tool.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_update_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_list_params.py',
   'PYMODULE'),
  ('openai.types.beta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_function_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_function_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_option_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_option_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_function',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_function.py',
   'PYMODULE'),
  ('openai.types.beta.thread_create_and_run_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\thread_create_and_run_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content_part_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message_content_part_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.threads.required_action_function_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\required_action_function_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_submit_tool_outputs_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\run_submit_tool_outputs_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_citation_delta_annotation',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\file_citation_delta_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_path_delta_annotation',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\file_path_delta_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_content_block',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_file_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_citation_annotation',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\file_citation_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_content_block',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_url_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_delta_block',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_file_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.refusal_content_block',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\refusal_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_update_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message_content_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_delta_block',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_url_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.file_path_annotation',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\file_path_annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.refusal_delta_block',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\refusal_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_content_block',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\text_content_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_update_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\run_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\run_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_include',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_include.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_tool_call_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.message_creation_step_details',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\message_creation_step_details.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_output_image',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_output_image.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta_message_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta_message_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.file_search_tool_call_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\file_search_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.function_tool_call_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\function_tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_calls_step_details',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_calls_step_details.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call_delta_object',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call_delta_object.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.file_search_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\file_search_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.code_interpreter_logs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\code_interpreter_logs.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.step_retrieve_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\step_retrieve_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.function_tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\function_tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.step_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\step_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.tool_call',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\tool_call.py',
   'PYMODULE'),
  ('openai.types.beta.threads.runs.run_step',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\runs\\run_step.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_delta_block',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\text_delta_block.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_file_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_file_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.annotation_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\annotation_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\run_list_params.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_deleted',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_content',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message_content.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_url_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_url_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_delta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\text_delta.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run_status',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\run_status.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_file.py',
   'PYMODULE'),
  ('openai.types.beta.threads.annotation',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\annotation.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_url.py',
   'PYMODULE'),
  ('openai.types.beta.threads.message',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\message.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\text.py',
   'PYMODULE'),
  ('openai.types.beta.threads.run',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\run.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_file_content_block_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_file_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.image_url_content_block_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\image_url_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.threads.text_content_block_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\threads\\text_content_block_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_option',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_option.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice_param.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_stream_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_stream_event.py',
   'PYMODULE'),
  ('openai.types.beta.assistant_tool_choice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\assistant_tool_choice.py',
   'PYMODULE'),
  ('openai.types.beta.thread_update_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\thread_update_params.py',
   'PYMODULE'),
  ('openai.types.beta.thread_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\thread_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.thread_deleted',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\thread_deleted.py',
   'PYMODULE'),
  ('openai.types.beta.thread',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\thread.py',
   'PYMODULE'),
  ('openai.resources.beta.threads',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\threads\\__init__.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.messages',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\threads\\messages.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.threads',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\threads\\threads.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs.runs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\runs.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs.steps',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\steps.py',
   'PYMODULE'),
  ('openai.resources.beta.threads.runs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\__init__.py',
   'PYMODULE'),
  ('openai.types.vector_store_search_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_store_search_response.py',
   'PYMODULE'),
  ('openai.types.file_chunking_strategy_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_object_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\static_file_chunking_strategy_object_param.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\static_file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.auto_file_chunking_strategy_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\auto_file_chunking_strategy_param.py',
   'PYMODULE'),
  ('openai.types.shared_params.metadata',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\shared_params\\metadata.py',
   'PYMODULE'),
  ('openai.types.vector_store_deleted',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_store_deleted.py',
   'PYMODULE'),
  ('openai.types.vector_store',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_store.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.file_batches',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\vector_stores\\file_batches.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file_batch',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file_batch.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file.py',
   'PYMODULE'),
  ('openai.types.file_chunking_strategy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\file_chunking_strategy.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy_object',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\static_file_chunking_strategy_object.py',
   'PYMODULE'),
  ('openai.types.static_file_chunking_strategy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\static_file_chunking_strategy.py',
   'PYMODULE'),
  ('openai.types.other_file_chunking_strategy_object',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\other_file_chunking_strategy_object.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_batch_list_files_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\file_batch_list_files_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_batch_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\file_batch_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\__init__.py',
   'PYMODULE'),
  ('openai.types.vector_stores.vector_store_file_deleted',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\vector_store_file_deleted.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_content_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\file_content_response.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_update_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\file_update_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_stores.file_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_stores\\file_list_params.py',
   'PYMODULE'),
  ('openai.types.file_object',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\file_object.py',
   'PYMODULE'),
  ('openai.pagination',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\pagination.py',
   'PYMODULE'),
  ('openai._resource',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_resource.py',
   'PYMODULE'),
  ('openai._compat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_compat.py',
   'PYMODULE'),
  ('openai.types.vector_store_update_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_store_update_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_search_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_store_search_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_store_create_params.py',
   'PYMODULE'),
  ('openai.types.vector_store_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\vector_store_list_params.py',
   'PYMODULE'),
  ('openai.resources.vector_stores.files',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\vector_stores\\files.py',
   'PYMODULE'),
  ('openai.resources.fine_tuning.fine_tuning',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\fine_tuning\\fine_tuning.py',
   'PYMODULE'),
  ('openai.resources.containers.containers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\containers\\containers.py',
   'PYMODULE'),
  ('openai.types.container_retrieve_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\container_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.container_create_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\container_create_response.py',
   'PYMODULE'),
  ('openai.types.container_list_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\container_list_response.py',
   'PYMODULE'),
  ('openai.types.container_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\container_create_params.py',
   'PYMODULE'),
  ('openai.types.container_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\container_list_params.py',
   'PYMODULE'),
  ('openai.resources.responses.responses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\responses\\responses.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._responses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\responses\\_responses.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\responses\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._events',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\responses\\_events.py',
   'PYMODULE'),
  ('openai.lib.streaming.responses._types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\responses\\_types.py',
   'PYMODULE'),
  ('openai.lib._parsing._responses',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\_parsing\\_responses.py',
   'PYMODULE'),
  ('openai.resources.uploads.uploads',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\uploads\\uploads.py',
   'PYMODULE'),
  ('openai.types.file_purpose',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\file_purpose.py',
   'PYMODULE'),
  ('openai.types.upload',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\upload.py',
   'PYMODULE'),
  ('openai.types.upload_complete_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\upload_complete_params.py',
   'PYMODULE'),
  ('openai.types.upload_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\upload_create_params.py',
   'PYMODULE'),
  ('openai.resources.moderations',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\moderations.py',
   'PYMODULE'),
  ('openai.types.moderation_multi_modal_input_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\moderation_multi_modal_input_param.py',
   'PYMODULE'),
  ('openai.types.moderation_image_url_input_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\moderation_image_url_input_param.py',
   'PYMODULE'),
  ('openai.types.moderation_text_input_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\moderation_text_input_param.py',
   'PYMODULE'),
  ('openai.types.moderation_create_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\moderation_create_response.py',
   'PYMODULE'),
  ('openai.types.moderation',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\moderation.py',
   'PYMODULE'),
  ('openai.types.moderation_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\moderation_model.py',
   'PYMODULE'),
  ('openai.types.moderation_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\moderation_create_params.py',
   'PYMODULE'),
  ('openai.resources.evals.evals',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\evals\\evals.py',
   'PYMODULE'),
  ('openai.types.eval_retrieve_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_retrieve_response.py',
   'PYMODULE'),
  ('openai.types.eval_stored_completions_data_source_config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_stored_completions_data_source_config.py',
   'PYMODULE'),
  ('openai.types.eval_custom_data_source_config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_custom_data_source_config.py',
   'PYMODULE'),
  ('openai.types.eval_update_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_update_response.py',
   'PYMODULE'),
  ('openai.types.eval_delete_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_delete_response.py',
   'PYMODULE'),
  ('openai.types.eval_create_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_create_response.py',
   'PYMODULE'),
  ('openai.types.eval_list_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_list_response.py',
   'PYMODULE'),
  ('openai.types.eval_update_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_update_params.py',
   'PYMODULE'),
  ('openai.types.eval_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_create_params.py',
   'PYMODULE'),
  ('openai.types.eval_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\eval_list_params.py',
   'PYMODULE'),
  ('openai.resources.completions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\completions.py',
   'PYMODULE'),
  ('openai.types.completion',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\completion.py',
   'PYMODULE'),
  ('openai.types.completion_choice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\completion_choice.py',
   'PYMODULE'),
  ('openai.types.completion_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\completion_create_params.py',
   'PYMODULE'),
  ('openai.resources.audio.audio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\audio\\audio.py',
   'PYMODULE'),
  ('openai.resources.embeddings',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\embeddings.py',
   'PYMODULE'),
  ('openai.types.create_embedding_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\create_embedding_response.py',
   'PYMODULE'),
  ('openai.types.embedding',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\embedding.py',
   'PYMODULE'),
  ('openai.types.embedding_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\embedding_model.py',
   'PYMODULE'),
  ('openai._extras',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_extras\\__init__.py',
   'PYMODULE'),
  ('openai._extras.sounddevice_proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_extras\\sounddevice_proxy.py',
   'PYMODULE'),
  ('openai._extras._common',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_extras\\_common.py',
   'PYMODULE'),
  ('openai._extras.pandas_proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_extras\\pandas_proxy.py',
   'PYMODULE'),
  ('openai._extras.numpy_proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_extras\\numpy_proxy.py',
   'PYMODULE'),
  ('openai.types.embedding_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\embedding_create_params.py',
   'PYMODULE'),
  ('openai.resources.chat.chat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\chat\\chat.py',
   'PYMODULE'),
  ('openai.resources.beta.beta',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\beta.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.realtime',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\realtime\\realtime.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\realtime\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_client_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_client_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncate_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncate_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_retrieve_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_retrieve_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_commit_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_commit_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_append_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_append_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_clear_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_clear_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_delete_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_delete_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_create_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_create_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_cancel_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_cancel_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_content_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_content_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_server_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_server_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_completed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_failed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_failed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_input_audio_transcription_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_input_audio_transcription_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_function_call_arguments_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_function_call_arguments_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_function_call_arguments_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_function_call_arguments_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_speech_stopped_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_speech_stopped_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_speech_started_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_speech_started_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_transcript_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_transcript_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_transcript_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_transcript_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_updated_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_committed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_committed_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_content_part_added_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_content_part_added_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncated_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_output_item_added_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_output_item_added_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_content_part_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_content_part_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_cleared_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_cleared_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_output_item_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_output_item_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_deleted_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_deleted_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_created_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_created_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_text_delta_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_text_delta_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_audio_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_audio_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.rate_limits_updated_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\rate_limits_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_text_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_text_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_created_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response_status',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response_status.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_response_usage',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_response_usage.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_updated_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\session_updated_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\session.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_created_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\session_created_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_done_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_done_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_content',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_content.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.error_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\error_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_client_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_client_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_truncate_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_truncate_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_retrieve_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_retrieve_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_commit_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_commit_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_append_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_append_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.input_audio_buffer_clear_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\input_audio_buffer_clear_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_delete_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_delete_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_create_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_create_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_update',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_update.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_create_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_create_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_with_reference',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_with_reference.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_cancel_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_cancel_event.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_update_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\session_update_event.py',
   'PYMODULE'),
  ('openai.types.websocket_connection_options',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\websocket_connection_options.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.transcription_sessions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\realtime\\transcription_sessions.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_create_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.transcription_session_update_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\transcription_session_update_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.response_create_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\response_create_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.conversation_item_with_reference_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\conversation_item_with_reference_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_update_event_param',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\session_update_event_param.py',
   'PYMODULE'),
  ('openai.types.beta.realtime',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\__init__.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_create_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\session_create_response.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.realtime_connect_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_connect_params.py',
   'PYMODULE'),
  ('openai.types.beta.realtime.session_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\beta\\realtime\\session_create_params.py',
   'PYMODULE'),
  ('openai.resources.beta.realtime.sessions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\beta\\realtime\\sessions.py',
   'PYMODULE'),
  ('openai.resources.webhooks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\webhooks.py',
   'PYMODULE'),
  ('openai.types.webhooks.unwrap_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\unwrap_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\__init__.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_succeeded_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_succeeded_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_cancelled_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.fine_tuning_job_failed_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\fine_tuning_job_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_incomplete_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\response_incomplete_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_completed_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\response_completed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_cancelled_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\response_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_succeeded_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\eval_run_succeeded_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_canceled_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\eval_run_canceled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.response_failed_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\response_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.eval_run_failed_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\eval_run_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_completed_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\batch_completed_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_cancelled_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\batch_cancelled_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_expired_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\batch_expired_webhook_event.py',
   'PYMODULE'),
  ('openai.types.webhooks.batch_failed_webhook_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\webhooks\\batch_failed_webhook_event.py',
   'PYMODULE'),
  ('openai.resources.batches',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\batches.py',
   'PYMODULE'),
  ('openai.types.batch',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\batch.py',
   'PYMODULE'),
  ('openai.types.batch_request_counts',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\batch_request_counts.py',
   'PYMODULE'),
  ('openai.types.batch_error',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\batch_error.py',
   'PYMODULE'),
  ('openai.types.batch_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\batch_create_params.py',
   'PYMODULE'),
  ('openai.types.batch_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\batch_list_params.py',
   'PYMODULE'),
  ('openai.resources.models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\models.py',
   'PYMODULE'),
  ('openai.types.model_deleted',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\model_deleted.py',
   'PYMODULE'),
  ('openai.types.model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\model.py',
   'PYMODULE'),
  ('openai.resources.images',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\images.py',
   'PYMODULE'),
  ('openai.types.image_edit_stream_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_edit_stream_event.py',
   'PYMODULE'),
  ('openai.types.image_edit_partial_image_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_edit_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.image_edit_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_edit_completed_event.py',
   'PYMODULE'),
  ('openai.types.image_gen_stream_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_gen_stream_event.py',
   'PYMODULE'),
  ('openai.types.image_gen_partial_image_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_gen_partial_image_event.py',
   'PYMODULE'),
  ('openai.types.image_gen_completed_event',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_gen_completed_event.py',
   'PYMODULE'),
  ('openai.types.images_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\images_response.py',
   'PYMODULE'),
  ('openai.types.image',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image.py',
   'PYMODULE'),
  ('openai.types.image_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_model.py',
   'PYMODULE'),
  ('openai.types.image_create_variation_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_create_variation_params.py',
   'PYMODULE'),
  ('openai.types.image_generate_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_generate_params.py',
   'PYMODULE'),
  ('openai.types.image_edit_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\image_edit_params.py',
   'PYMODULE'),
  ('openai.resources.files',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\resources\\files.py',
   'PYMODULE'),
  ('openai.types.file_deleted',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\file_deleted.py',
   'PYMODULE'),
  ('openai.types.file_create_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\file_create_params.py',
   'PYMODULE'),
  ('openai.types.file_list_params',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\file_list_params.py',
   'PYMODULE'),
  ('httpx',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\__init__.py',
   'PYMODULE'),
  ('httpx._main',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_main.py',
   'PYMODULE'),
  ('httpcore',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.trio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_backends\\trio.py',
   'PYMODULE'),
  ('httpcore._backends',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_backends\\__init__.py',
   'PYMODULE'),
  ('httpcore._backends.anyio',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_backends\\anyio.py',
   'PYMODULE'),
  ('httpcore._utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_utils.py',
   'PYMODULE'),
  ('httpcore._sync',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_sync\\__init__.py',
   'PYMODULE'),
  ('httpcore._sync.socks_proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_sync\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._trace',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_trace.py',
   'PYMODULE'),
  ('httpcore._synchronization',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_synchronization.py',
   'PYMODULE'),
  ('httpcore._sync.http2',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_sync\\http2.py',
   'PYMODULE'),
  ('httpcore._sync.interfaces',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_sync\\interfaces.py',
   'PYMODULE'),
  ('httpcore._sync.http_proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_sync\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._sync.http11',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_sync\\http11.py',
   'PYMODULE'),
  ('h11',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._util',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._state',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._events',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._abnf',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._writers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._readers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('httpcore._sync.connection_pool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_sync\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._sync.connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_sync\\connection.py',
   'PYMODULE'),
  ('httpcore._ssl',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_ssl.py',
   'PYMODULE'),
  ('certifi',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('httpcore._models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_models.py',
   'PYMODULE'),
  ('httpcore._exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_exceptions.py',
   'PYMODULE'),
  ('httpcore._backends.sync',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_backends\\sync.py',
   'PYMODULE'),
  ('httpcore._backends.mock',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_backends\\mock.py',
   'PYMODULE'),
  ('httpcore._backends.base',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_backends\\base.py',
   'PYMODULE'),
  ('httpcore._async',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_async\\__init__.py',
   'PYMODULE'),
  ('httpcore._async.socks_proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_async\\socks_proxy.py',
   'PYMODULE'),
  ('httpcore._backends.auto',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_backends\\auto.py',
   'PYMODULE'),
  ('httpcore._async.http2',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_async\\http2.py',
   'PYMODULE'),
  ('httpcore._async.interfaces',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_async\\interfaces.py',
   'PYMODULE'),
  ('httpcore._async.http_proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_async\\http_proxy.py',
   'PYMODULE'),
  ('httpcore._async.http11',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_async\\http11.py',
   'PYMODULE'),
  ('httpcore._async.connection_pool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_async\\connection_pool.py',
   'PYMODULE'),
  ('httpcore._async.connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_async\\connection.py',
   'PYMODULE'),
  ('httpcore._api',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpcore\\_api.py',
   'PYMODULE'),
  ('httpx._urls',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_urls.py',
   'PYMODULE'),
  ('httpx._utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_utils.py',
   'PYMODULE'),
  ('httpx._urlparse',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_urlparse.py',
   'PYMODULE'),
  ('httpx._types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_types.py',
   'PYMODULE'),
  ('httpx._transports',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_transports\\__init__.py',
   'PYMODULE'),
  ('httpx._transports.wsgi',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_transports\\wsgi.py',
   'PYMODULE'),
  ('httpx._transports.mock',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_transports\\mock.py',
   'PYMODULE'),
  ('httpx._transports.default',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_transports\\default.py',
   'PYMODULE'),
  ('httpx._transports.base',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_transports\\base.py',
   'PYMODULE'),
  ('httpx._transports.asgi',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_transports\\asgi.py',
   'PYMODULE'),
  ('httpx._status_codes',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_status_codes.py',
   'PYMODULE'),
  ('httpx._models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_models.py',
   'PYMODULE'),
  ('httpx._multipart',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_multipart.py',
   'PYMODULE'),
  ('httpx._decoders',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_decoders.py',
   'PYMODULE'),
  ('httpx._exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_exceptions.py',
   'PYMODULE'),
  ('httpx._content',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_content.py',
   'PYMODULE'),
  ('httpx._config',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_config.py',
   'PYMODULE'),
  ('httpx._client',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_client.py',
   'PYMODULE'),
  ('httpx._auth',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_auth.py',
   'PYMODULE'),
  ('httpx._api',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\_api.py',
   'PYMODULE'),
  ('httpx.__version__',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\httpx\\__version__.py',
   'PYMODULE'),
  ('openai.lib.streaming',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\__init__.py',
   'PYMODULE'),
  ('openai.lib.streaming._assistants',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\streaming\\_assistants.py',
   'PYMODULE'),
  ('openai.lib._old_api',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\_old_api.py',
   'PYMODULE'),
  ('openai.version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\version.py',
   'PYMODULE'),
  ('openai.lib.azure',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\azure.py',
   'PYMODULE'),
  ('openai.lib',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\lib\\__init__.py',
   'PYMODULE'),
  ('openai._utils._resources_proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\_resources_proxy.py',
   'PYMODULE'),
  ('openai._utils._proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\_proxy.py',
   'PYMODULE'),
  ('openai._legacy_response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_legacy_response.py',
   'PYMODULE'),
  ('openai._utils._logs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\_logs.py',
   'PYMODULE'),
  ('openai._base_client',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_base_client.py',
   'PYMODULE'),
  ('openai._qs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_qs.py',
   'PYMODULE'),
  ('distro',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\distro\\__init__.py',
   'PYMODULE'),
  ('distro.distro',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\distro\\distro.py',
   'PYMODULE'),
  ('openai._exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_exceptions.py',
   'PYMODULE'),
  ('openai._constants',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_constants.py',
   'PYMODULE'),
  ('openai._response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_response.py',
   'PYMODULE'),
  ('openai._version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_version.py',
   'PYMODULE'),
  ('openai._models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_models.py',
   'PYMODULE'),
  ('openai._client',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_client.py',
   'PYMODULE'),
  ('openai._utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\__init__.py',
   'PYMODULE'),
  ('openai._utils._reflection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\_reflection.py',
   'PYMODULE'),
  ('openai._utils._streams',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\_streams.py',
   'PYMODULE'),
  ('openai._utils._sync',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_utils\\_sync.py',
   'PYMODULE'),
  ('openai._types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\_types.py',
   'PYMODULE'),
  ('openai.types',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\__init__.py',
   'PYMODULE'),
  ('openai.types.file_content',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\file_content.py',
   'PYMODULE'),
  ('openai.types.chat_model',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\openai\\types\\chat_model.py',
   'PYMODULE'),
  ('typing_extensions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('voice.google.google_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\google\\google_voice.py',
   'PYMODULE'),
  ('voice.google', '-', 'PYMODULE'),
  ('voice.baidu.baidu_voice',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\voice\\baidu\\baidu_voice.py',
   'PYMODULE'),
  ('voice.baidu', '-', 'PYMODULE'),
  ('translate.factory',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\translate\\factory.py',
   'PYMODULE'),
  ('translate',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\translate\\__init__.py',
   'PYMODULE'),
  ('translate.baidu.baidu_translate',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\translate\\baidu\\baidu_translate.py',
   'PYMODULE'),
  ('translate.baidu', '-', 'PYMODULE'),
  ('translate.translator',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\translate\\translator.py',
   'PYMODULE'),
  ('bot.bot_factory',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\bot_factory.py',
   'PYMODULE'),
  ('bot.modelscope.modelscope_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\modelscope\\modelscope_bot.py',
   'PYMODULE'),
  ('bot.modelscope', '-', 'PYMODULE'),
  ('bot.modelscope.modelscope_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\modelscope\\modelscope_session.py',
   'PYMODULE'),
  ('bot.session_manager',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\session_manager.py',
   'PYMODULE'),
  ('bot.deepseek.deepseek_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\deepseek\\deepseek_bot.py',
   'PYMODULE'),
  ('bot.deepseek',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\deepseek\\__init__.py',
   'PYMODULE'),
  ('bot.deepseek.deepseek_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\deepseek\\deepseek_session.py',
   'PYMODULE'),
  ('bot.minimax.minimax_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\minimax\\minimax_bot.py',
   'PYMODULE'),
  ('bot.minimax', '-', 'PYMODULE'),
  ('bot.chatgpt.chat_gpt_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\chatgpt\\chat_gpt_session.py',
   'PYMODULE'),
  ('bot.chatgpt', '-', 'PYMODULE'),
  ('bot.minimax.minimax_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\minimax\\minimax_session.py',
   'PYMODULE'),
  ('bot.moonshot.moonshot_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\moonshot\\moonshot_bot.py',
   'PYMODULE'),
  ('bot.moonshot', '-', 'PYMODULE'),
  ('bot.moonshot.moonshot_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\moonshot\\moonshot_session.py',
   'PYMODULE'),
  ('bot.bytedance.bytedance_coze_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\bytedance\\bytedance_coze_bot.py',
   'PYMODULE'),
  ('bot.bytedance', '-', 'PYMODULE'),
  ('bot.bytedance.coze_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\bytedance\\coze_session.py',
   'PYMODULE'),
  ('bot.bytedance.coze_client',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\bytedance\\coze_client.py',
   'PYMODULE'),
  ('bot.zhipuai.zhipuai_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\zhipuai\\zhipuai_bot.py',
   'PYMODULE'),
  ('bot.zhipuai', '-', 'PYMODULE'),
  ('bot.zhipuai.zhipu_ai_image',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\zhipuai\\zhipu_ai_image.py',
   'PYMODULE'),
  ('bot.zhipuai.zhipu_ai_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\zhipuai\\zhipu_ai_session.py',
   'PYMODULE'),
  ('bot.dify.dify_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\dify\\dify_bot.py',
   'PYMODULE'),
  ('bot.dify', '-', 'PYMODULE'),
  ('bot.dify.dify_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\dify\\dify_session.py',
   'PYMODULE'),
  ('bot.gemini.google_gemini_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\gemini\\google_gemini_bot.py',
   'PYMODULE'),
  ('bot.gemini', '-', 'PYMODULE'),
  ('bot.baidu.baidu_wenxin_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\baidu\\baidu_wenxin_session.py',
   'PYMODULE'),
  ('bot.baidu', '-', 'PYMODULE'),
  ('bot.dashscope.dashscope_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\dashscope\\dashscope_bot.py',
   'PYMODULE'),
  ('bot.dashscope', '-', 'PYMODULE'),
  ('bot.dashscope.dashscope_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\dashscope\\dashscope_session.py',
   'PYMODULE'),
  ('bot.ali.ali_qwen_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\ali\\ali_qwen_bot.py',
   'PYMODULE'),
  ('bot.ali', '-', 'PYMODULE'),
  ('bot.openai.open_ai_vision',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\openai\\open_ai_vision.py',
   'PYMODULE'),
  ('bot.openai', '-', 'PYMODULE'),
  ('bot.openai.open_ai_image',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\openai\\open_ai_image.py',
   'PYMODULE'),
  ('common.token_bucket',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\common\\token_bucket.py',
   'PYMODULE'),
  ('bot.ali.ali_qwen_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\ali\\ali_qwen_session.py',
   'PYMODULE'),
  ('bot.claudeapi.claude_api_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\claudeapi\\claude_api_bot.py',
   'PYMODULE'),
  ('bot.claudeapi', '-', 'PYMODULE'),
  ('bot.claude.claude_ai_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\claude\\claude_ai_bot.py',
   'PYMODULE'),
  ('bot.claude', '-', 'PYMODULE'),
  ('bot.claude.claude_ai_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\claude\\claude_ai_session.py',
   'PYMODULE'),
  ('bot.linkai.link_ai_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\linkai\\link_ai_bot.py',
   'PYMODULE'),
  ('bot.linkai', '-', 'PYMODULE'),
  ('bot.xunfei.xunfei_spark_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\xunfei\\xunfei_spark_bot.py',
   'PYMODULE'),
  ('bot.xunfei', '-', 'PYMODULE'),
  ('bot.openai.open_ai_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\openai\\open_ai_bot.py',
   'PYMODULE'),
  ('bot.openai.open_ai_session',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\openai\\open_ai_session.py',
   'PYMODULE'),
  ('bot.chatgpt.chat_gpt_bot',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\chatgpt\\chat_gpt_bot.py',
   'PYMODULE'),
  ('bot.baidu.baidu_wenxin',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\bot\\baidu\\baidu_wenxin.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uuid.py',
   'PYMODULE'),
  ('requests',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.hooks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.packages',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('ntwork',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\__init__.py',
   'PYMODULE'),
  ('ntwork.exception',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\exception\\__init__.py',
   'PYMODULE'),
  ('ntwork.wc',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\wc\\__init__.py',
   'PYMODULE'),
  ('ntwork.core.wework',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\core\\wework.py',
   'PYMODULE'),
  ('ntwork.core',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\core\\__init__.py',
   'PYMODULE'),
  ('ntwork.utils.logger',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\utils\\logger.py',
   'PYMODULE'),
  ('ntwork.utils',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\utils\\__init__.py',
   'PYMODULE'),
  ('ntwork.core.mgr',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\core\\mgr.py',
   'PYMODULE'),
  ('ntwork.utils.singleton',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\utils\\singleton.py',
   'PYMODULE'),
  ('ntwork.utils.xdg',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\utils\\xdg.py',
   'PYMODULE'),
  ('pyee',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pyee\\__init__.py',
   'PYMODULE'),
  ('pyee.base',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pyee\\base.py',
   'PYMODULE'),
  ('ntwork.conf',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\conf\\__init__.py',
   'PYMODULE'),
  ('PyQt5',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE')],
 [('ntwork\\wc\\helper_4.0.8.6027.dat',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\wc\\helper_4.0.8.6027.dat',
   'BINARY'),
  ('python310.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp310-win_amd64.pyd',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\pydantic_core\\_pydantic_core.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('jiter\\jiter.cp310-win_amd64.pyd',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\jiter\\jiter.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('ntwork\\wc\\wcprobe.cp310-win_amd64.pyd',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\ntwork\\wc\\wcprobe.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp310-win_amd64.pyd',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libffi-7.dll',
   'BINARY')],
 [],
 [],
 [('certifi\\py.typed',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('base_library.zip',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\build\\WeworkAutoReply\\base_library.zip',
   'DATA')],
 [('enum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\enum.py',
   'PYMODULE'),
  ('locale',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\locale.py',
   'PYMODULE'),
  ('weakref',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\weakref.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\genericpath.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('sre_parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sre_parse.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('copyreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\copyreg.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sre_compile.py',
   'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('codecs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\codecs.py',
   'PYMODULE'),
  ('io',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\io.py',
   'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sre_constants.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ntpath.py',
   'PYMODULE'),
  ('types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\types.py',
   'PYMODULE'),
  ('keyword',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\keyword.py',
   'PYMODULE'),
  ('functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\functools.py',
   'PYMODULE'),
  ('abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\abc.py',
   'PYMODULE'),
  ('heapq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\heapq.py',
   'PYMODULE'),
  ('warnings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\warnings.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('linecache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\linecache.py',
   'PYMODULE'),
  ('reprlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\reprlib.py',
   'PYMODULE'),
  ('operator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\operator.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\stat.py',
   'PYMODULE'),
  ('re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\re.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\posixpath.py',
   'PYMODULE'),
  ('traceback',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\traceback.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\os.py',
   'PYMODULE')])
