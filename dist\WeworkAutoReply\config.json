{"app_name": "企业微信自动回复系统", "app_version": "1.0.0", "debug": true, "window_width": 800, "window_height": 600, "window_title": "企业微信自动回复系统", "minimize_to_tray": true, "start_minimized": false, "mock_enabled": true, "mock_auto_reply": true, "mock_reply_text": "测试回复消息", "mock_reply_delay": 2, "mock_message_interval": 10, "log_level": "INFO", "log_to_file": true, "log_file_path": "logs/app.log", "log_max_size": 10, "log_backup_count": 5, "tray_enabled": true, "tray_show_notifications": true, "tray_notification_duration": 3000, "auto_reply_enabled": true, "reply_prefix": "[自动回复] ", "reply_suffix": "", "service_status": "stopped", "last_activity": null, "message_count": 0, "reply_count": 0}