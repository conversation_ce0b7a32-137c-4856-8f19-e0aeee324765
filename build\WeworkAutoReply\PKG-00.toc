('E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\build\\WeworkAutoReply\\WeworkAutoReply.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\build\\WeworkAutoReply\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\build\\WeworkAutoReply\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\build\\WeworkAutoReply\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\build\\WeworkAutoReply\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\build\\WeworkAutoReply\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\build\\WeworkAutoReply\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\venv_build\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main', 'E:\\Desktop\\企业微信自动回复系统\\企业微信自动回复系统\\app\\main.py', 'PYSOURCE')],
 'python310.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
