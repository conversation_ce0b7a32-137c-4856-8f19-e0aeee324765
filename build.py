#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业微信自动回复系统打包脚本
使用PyInstaller打包成单目录可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build():
    """清理之前的构建文件"""
    print("🧹 清理之前的构建文件...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   删除目录: {dir_name}")
    
    # 清理.spec文件
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        spec_file.unlink()
        print(f"   删除文件: {spec_file}")

def build_app():
    """使用PyInstaller打包应用"""
    print("📦 开始打包应用...")
    
    # 根据ntwork官方文档的打包命令
    cmd = [
        'pyinstaller',
        '-y',  # 覆盖输出目录
        '--collect-data=ntwork',  # 收集ntwork数据文件
        '--paths=.',  # 添加当前目录到Python路径
        '--name=企业微信自动回复系统',  # 设置应用名称
        '--icon=2.ico',  # 设置图标（如果存在）
        '--noconsole',  # 不显示控制台窗口（GUI应用）
        '--onedir',  # 打包成单目录
        'main.py'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def copy_resources():
    """复制资源文件到打包目录"""
    print("📁 复制资源文件...")
    
    dist_dir = Path('dist/企业微信自动回复系统')
    if not dist_dir.exists():
        print("❌ 打包目录不存在")
        return False
    
    # 需要复制的文件和目录
    resources = [
        'config.json',
        'config-gui.json',
        'tmp',
        '2.ico',
        '12.jpg',
        '33.png'
    ]
    
    for resource in resources:
        src = Path(resource)
        if src.exists():
            dst = dist_dir / resource
            if src.is_dir():
                if dst.exists():
                    shutil.rmtree(dst)
                shutil.copytree(src, dst)
                print(f"   复制目录: {resource}")
            else:
                shutil.copy2(src, dst)
                print(f"   复制文件: {resource}")
        else:
            print(f"   跳过不存在的资源: {resource}")
    
    return True

def create_readme():
    """创建使用说明文件"""
    print("📝 创建使用说明...")
    
    readme_content = """# 企业微信自动回复系统

## 使用说明

1. 双击 `企业微信自动回复系统.exe` 启动程序
2. 首次运行会自动创建配置文件
3. 在GUI界面中配置自动回复内容
4. 点击"启动服务"开始自动回复

## 系统要求

- Windows 10 或更高版本
- 企业微信客户端 4.0.8.6027 版本

## 配置文件说明

- `config.json`: 基础配置文件
- `config-gui.json`: GUI界面配置文件
- `tmp/`: 临时文件目录

## 注意事项

1. 请确保企业微信客户端已安装并登录
2. 首次使用需要配置自动回复内容
3. 程序运行时请勿关闭企业微信客户端

## 技术支持

如有问题请联系技术支持。

---
版本: 1.0.0
构建时间: """ + str(Path().resolve()) + """
"""
    
    dist_dir = Path('dist/企业微信自动回复系统')
    readme_file = dist_dir / 'README.txt'
    
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"   创建说明文件: {readme_file}")

def main():
    """主函数"""
    print("🚀 企业微信自动回复系统打包工具")
    print("=" * 50)
    
    # 检查主程序文件是否存在
    if not os.path.exists('main.py'):
        print("❌ 找不到 main.py 文件")
        return False
    
    # 清理构建文件
    clean_build()
    
    # 打包应用
    if not build_app():
        return False
    
    # 复制资源文件
    if not copy_resources():
        return False
    
    # 创建说明文件
    create_readme()
    
    print("\n🎉 打包完成！")
    print(f"📂 输出目录: {Path('dist/企业微信自动回复系统').resolve()}")
    print("\n使用方法:")
    print("1. 进入 dist/企业微信自动回复系统 目录")
    print("2. 双击 企业微信自动回复系统.exe 启动程序")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按任意键退出...")
        sys.exit(1)
