[INFO][2025-08-20 21:12:53][wework_channel.py:190] - 等待登录······
[INFO][2025-08-20 21:12:55][wework_channel.py:195] - 登录信息:>>>user_id:1688851272749679>>>>>>>>name:童树运
[INFO][2025-08-20 21:12:55][wework_channel.py:196] - 企业微信登录成功，开始获取联系人和群聊信息...
[WARNING][2025-08-20 21:12:55][wework_channel.py:173] - 获取数据失败，重试第1次······
[INFO][2025-08-20 21:13:00][wework_channel.py:229] - wework程序初始化完成········
[INFO][2025-08-20 21:13:08][bridge.py:80] - create bot chatGPT for chat
[INFO][2025-08-20 21:13:08][chat_gpt_bot.py:60] - [CHATGPT] query=1
[ERROR][2025-08-20 21:13:08][chat_gpt_bot.py:211] - [CHATGPT] Exception: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
Traceback (most recent call last):
  File "E:\Desktop\企业微信自动回复系统\企业微信自动回复系统\app\bot\chatgpt\chat_gpt_bot.py", line 137, in reply_text
    response = openai.ChatCompletion.create(api_key=api_key, messages=session.messages, **args)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\chat_completion.py", line 25, in create
    return super().create(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 149, in create
    ) = cls.__prepare_create_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_resources\abstract\engine_api_resource.py", line 106, in __prepare_create_request
    requestor = api_requestor.APIRequestor(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\api_requestor.py", line 138, in __init__
    self.api_key = key or util.default_api_key()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\openai\util.py", line 186, in default_api_key
    raise openai.error.AuthenticationError(
openai.error.AuthenticationError: No API key provided. You can set your API key in code using 'openai.api_key = <API-KEY>', or you can set the environment variable OPENAI_API_KEY=<API-KEY>). If your API key is stored in a file, you can point the openai module at it with 'openai.api_key_path = <PATH>'. You can generate API keys in the OpenAI web interface. See https://platform.openai.com/account/api-keys for details.
[INFO][2025-08-20 21:13:08][wework_channel.py:292] - [WX] sendMsg=Reply(type=ERROR, content=[ERROR]
我现在有点累了，等会再来吧), receiver=S:1688851272749679_1688856659680639
