#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复版本的打包脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_spec_file():
    """创建自定义的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 收集所有项目模块
a = Analysis(
    ['main.py'],
    pathex=['.'],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('config-gui.json', '.'),
        ('2.ico', '.'),
        ('tmp', 'tmp'),
        ('gui', 'gui'),
        ('bot', 'bot'),
        ('bridge', 'bridge'),
        ('channel', 'channel'),
        ('common', 'common'),
        ('plugins', 'plugins'),
        ('translate', 'translate'),
        ('voice', 'voice'),
    ],
    hiddenimports=[
        'gui_app',
        'config',
        'gui.main_window',
        'gui.config_loader',
        'gui.controllers',
        'gui.managers',
        'gui.system_tray',
        'bot.mock.mock_bot',
        'bridge.bridge',
        'bridge.reply',
        'channel.wework.wework_channel',
        'common.const',
        'common.log',
        'ntwork',
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # 先设置为True以便调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='2.ico',
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='企业微信自动回复系统',
)
'''
    
    with open('WeworkAutoReply.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 创建自定义spec文件成功")

def main():
    print("🚀 企业微信自动回复系统 - 修复版打包")
    print("=" * 50)
    
    # 清理之前的构建
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("✅ 清理dist目录")
    
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("✅ 清理build目录")
    
    # 删除旧的spec文件
    spec_files = list(Path('.').glob('*.spec'))
    for spec_file in spec_files:
        spec_file.unlink()
        print(f"✅ 删除旧spec文件: {spec_file}")
    
    # 创建新的spec文件
    create_spec_file()
    
    # 使用spec文件打包
    cmd = [
        'venv_build\\Scripts\\pyinstaller.exe',
        'WeworkAutoReply.spec'
    ]
    
    print(f"📦 执行打包命令:")
    print(f"   {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, cwd='.')
        print("✅ 打包成功！")
        
        # 检查输出目录
        dist_dir = Path('dist/企业微信自动回复系统')
        if dist_dir.exists():
            print(f"\n🎉 打包完成！")
            print(f"📂 输出目录: {dist_dir.resolve()}")
            print("\n使用方法:")
            print("1. 进入 dist/企业微信自动回复系统 目录")
            print("2. 双击 企业微信自动回复系统.exe 启动程序")
        else:
            print("❌ 输出目录不存在")
            return False
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按任意键退出...")
        sys.exit(1)
    else:
        input("\n打包完成，按任意键退出...")
