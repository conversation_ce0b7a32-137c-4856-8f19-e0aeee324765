#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
企业微信自动回复系统 - 主程序入口
"""

import os
import sys
import traceback

# 设置环境变量，关闭ntwork日志
os.environ['NTWORK_LOG'] = "ERROR"

def main():
    """主程序入口"""
    try:
        # 导入GUI应用
        from gui_app import main as gui_main
        
        print("正在启动企业微信自动回复系统...")
        print("版本: 1.0.0")
        print("作者: 企业微信自动回复系统")
        print("-" * 50)
        
        # 启动GUI应用
        gui_main()
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保所有依赖已正确安装")
        input("按任意键退出...")
        sys.exit(1)
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        input("按任意键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()
