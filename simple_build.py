#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的打包脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    print("开始简化打包...")
    
    # 清理之前的构建
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("清理dist目录")
    
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("清理build目录")
    
    # 简化的打包命令
    cmd = [
        'pyinstaller',
        '--onedir',
        '--windowed',
        '--name=WeworkAutoReply',
        'main.py'
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("打包成功！")
        
        # 复制配置文件
        dist_dir = Path('dist/WeworkAutoReply')
        if dist_dir.exists():
            print("复制配置文件...")
            
            files_to_copy = ['config.json', 'config-gui.json']
            for file in files_to_copy:
                if os.path.exists(file):
                    shutil.copy2(file, dist_dir)
                    print(f"复制: {file}")
            
            # 复制tmp目录
            if os.path.exists('tmp'):
                dst_tmp = dist_dir / 'tmp'
                if dst_tmp.exists():
                    shutil.rmtree(dst_tmp)
                shutil.copytree('tmp', dst_tmp)
                print("复制: tmp目录")
            
            print(f"\n打包完成！输出目录: {dist_dir.resolve()}")
            print("运行方法: 双击 WeworkAutoReply.exe")
        
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("按任意键退出...")
        sys.exit(1)
    else:
        input("打包完成，按任意键退出...")
